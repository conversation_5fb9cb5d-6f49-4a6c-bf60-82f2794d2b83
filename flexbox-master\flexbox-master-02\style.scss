html {
  font-size: 62.5%;
}

body {
  font-size: 1.6rem;
  font-family: "Poppins", sans-serif;
  font-weight: normal;
  background-color: #f3faff;
}

a {
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
}

@mixin flexbox($align: flex-start, $justify: flex-start, $flex-direction: row, $wrap: nowrap) {
  display: flex;
  align-items: $align;
  justify-content: $justify;
  flex-direction: $flex-direction;
  flex-wrap: $wrap;
}

.post {
  &__list {
    padding: 2rem;
    @include flexbox(stretch);
    margin-left: -2rem;
  }

  &__item {
    width: calc(33.33% - 2rem);
    margin-left: 2rem;
  }

  &__image {
    width: 100%;
    height: 20rem;
    object-fit: cover;
  }

  &__content {
    padding: 2rem;
    height: calc(100% - 20rem);
    background-color: white;
    border: solid 1px #eee;
    @include flexbox(flex-start, flex-start, column);
  }

  &__title {
    font-size: 2rem;
    line-height: 2;
  }

  &__author {
    width: 5rem;
    height: 5rem;
    border-radius: 5rem;
    object-fit: cover;
    margin-top: auto;
  }
}