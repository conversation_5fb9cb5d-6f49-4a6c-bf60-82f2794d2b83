*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.images {
  padding: 25px;
  display: grid;
  grid-gap: 36px;

  /* grid-template-columns: repeat(4, 1fr); */
  /* grid-template-columns: repeat(auto-fit, value); */
  /* grid-template-columns: repeat(auto-fill, value); */
  /* minmax(minimum value, maximum value) */
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  grid-template-columns: repeat(auto-fit, minmax(293px, 1fr));
}

/* @media screen and (max-width: 1279px) {
    .images {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media screen and (max-width: 1023px) {
    .images {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media screen and (max-width: 600px) {
    .images {
      grid-template-columns: repeat(1, 1fr);
    }
} */

.image-item {
  border-radius: 10px;
  height: 220px;
  box-shadow: rgba(0, 0, 0, 0.5) 0 4px 12px;
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

@media screen and (max-width: 1023px) {
  .images {
    grid-template-columns: unset;
    grid-auto-flow: column;
    grid-auto-columns: 80%;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-snap-stop: always;
    padding: 25px;
    scroll-padding: 25px;
    margin-left: -25px;
  }

  .images {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .images::-webkit-scrollbar {
    display: none;
  }

  .image-item {
    scroll-snap-align: start;
  }
}
