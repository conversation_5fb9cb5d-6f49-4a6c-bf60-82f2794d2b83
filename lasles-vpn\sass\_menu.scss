.menu {
  list-style: none;
  display: flex;
  align-items: center;
  gap: 0 40px;

  &-link {
    color: inherit;
  }

  &-toggle {
    display: none;
    width: 30px;
    height: 20px;

    span {
      pointer-events: none;
      width: 100%;
      height: 3px;
      background-color: $heading-color;
      display: block;
    }
  }
}

@media screen and (max-width: 767px) {
  .menu {
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: white;
    width: 300px;
    z-index: 99;
    border-left: 1px solid $gray-lighter-color;
    flex-direction: column;
    gap: 10px 0;
    padding-top: 60px;
    align-items: stretch;
    transform: translateX(100%);
    will-change: transform;
    transition: all 0.2s linear;

    &.is-show {
      transform: translateX(0);
    }
  }

  .menu-link {
    display: block;
    width: 100%;
    padding: 20px;
  }

  .menu-toggle {
    display: inline-flex;
    justify-content: space-between;
    flex-direction: column;
  }

  .menu-item--auth {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 20px;
  }
}

@media (hover: hover) {
  .menu-link {
    position: relative;
    transition: all 0.2s linear;

    &::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: auto;
      right: 0;
      width: 0;
      height: 3px;
      background-color: $primary-color;
      transition: all 0.2s linear;
    }

    &:hover {
      color: $primary-color;

      &::after {
        width: 100%;
        left: 0;
        right: auto;
      }
    }
  }
}

@media screen and (min-width: 768px) {
  .menu-item--auth {
    display: none;
  }
}