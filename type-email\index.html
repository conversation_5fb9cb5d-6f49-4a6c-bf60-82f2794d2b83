<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Type Email Landing Page</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Sora:wght@300;400;600&amp;display=swap">
    <link rel="stylesheet" href="./styles/app.css">
  </head>
  <body> 
    <div class="wrapper"> 
      <header class="header">
        <div class="container">
          <div class="header-top"><a class="header-logo" href="./index.html"><img src="./images/logo.svg" alt="logo"><span class="header-logo-text">Type Email</span></a>
            <ul class="menu"> 
              <li class="menu-item"><a class="menu-link" href="./index.html">Home</a></li>
              <li class="menu-item"><a class="menu-link" href="#">Pricing</a></li>
              <li class="menu-item"><a class="menu-link" href="#">Resources</a></li>
              <li class="menu-item"><a class="menu-link" href="#">Support</a></li>
            </ul><a class="header-login" href="#">Login</a><span class="menu-toggle"> 
              <svg width="13" height="12" viewbox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg"> 
                <rect width="13" height="2" fill="#82E263"></rect>
                <rect y="5" width="13" height="2" fill="#82E263"></rect>
                <rect y="10" width="13" height="2" fill="#82E263"></rect>
              </svg></span>
          </div>
          <div class="header-content">
            <h1 class="header-heading text-primary"> Online shoppers into loyal, lifetime customers with email & sms marketing</h1>
            <div class="header-buttons"><a class="header-started button button--primary" href="#">Get Started</a><a class="header-works" href="#"><span>How it works</span><img src="./images/icon-play.svg" alt="play"></a></div>
          </div>
          <div class="header-image"><img srcSet="./images/image-1.png 2x" alt="header image"></div>
        </div>
      </header>
      <div class="main"> 
        <section class="countdown">
          <div class="container">
            <div class="countdown-list">
              <div class="countdown-item">
                <h3 class="countdown-title">1,700+</h3>
                <p class="countdown-text">Companies using Sendlance</p>
              </div>
              <div class="countdown-item">
                <h3 class="countdown-title">40 million+</h3>
                <p class="countdown-text">Emails sent daily</p>
              </div>
              <div class="countdown-item">
                <h3 class="countdown-title">20.000+</h3>
                <p class="countdown-text">Users on sendlance</p>
              </div>
            </div>
          </div>
        </section>
        <section class="newsletter">
          <div class="container"> 
            <div class="newsletter-header"> 
              <h2 class="heading heading--big">More than just drips and newsletters</h2>
              <p class="text">awareness through email became a must-have, and our ESP wasn't cutting</p>
            </div>
          </div>
          <div class="newsletter-main">
            <div class="newsletter-container">
              <div class="newsletter-content"> 
                <h3 class="heading heading--normal newsletter-title">Drive 30-40% of your revenue with email marekiting</h3>
                <p class="text newsletter-desc">Unlock customer insights to grow your business faster. Store all your customer data collected from storefront to marketing channels in one central hub far easy use in every strategy. </p>
                <div class="newsletter-list"> 
                  <div class="newsletter-item"><img class="newsletter-icon" srcSet="./images/icon-home.png 2x" alt="Home Icon">
                    <h4 class="heading heading--small newsletter-name">Marketing Automation</h4>
                    <p class="text newsletter-text">From open rates to conversion performance, get access to tons of data.</p><a class="newsletter-link" href="#">Read More</a>
                  </div>
                  <div class="newsletter-item"><img class="newsletter-icon" srcSet="./images/icon-python.png 2x" alt="Python Icon">
                    <h4 class="heading heading--small newsletter-name">Experiments</h4>
                    <p class="text newsletter-text">From sign up to sale track your contacts every move.</p><a class="newsletter-link" href="#">Read More </a>
                  </div>
                </div>
              </div>
              <div class="newsletter-image"> <img srcSet="./images/image-2.png 2x" alt="Newsletter Image"></div>
            </div>
          </div>
        </section>
        <div class="feature"> 
          <div class="container"> 
            <div class="feature-main">
              <div class="feature-image"><img srcSet="./images/image-3.png 2x" alt="Feature Image"></div>
              <div class="feature-content"> 
                <h3 class="heading heading--normal feature-title">Create raving fans that keep buying over and over you get the point.</h3>
                <p class="text feature-text">Unlock customer insights to grow your business faster. Store all your customer data collected from storefront to marketing channels in one central hub far easy use in every strategy. </p>
                <p class="text">Our open rate has tripled and our click rate on links is up at least 15%.</p><a class="button button--primary feature-link" href="#">Learn more </a>
              </div>
            </div>
          </div>
        </div>
        <section class="service">
          <div class="container">
            <h2 class="heading heading--big service-heading">Our some service</h2>
            <div class="service-list">
              <div class="service-item">
                <div class="service-icon">
                  <svg width="24" height="17" viewbox="0 0 24 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 14.875V2.125C24 0.949167 23.0547 0 21.8836 0H2.1164C0.945326 0 0 0.949167 0 2.125V14.875C0 16.0508 0.945326 17 2.1164 17H21.8836C23.0547 17 24 16.0508 24 14.875ZM22.1517 1.96917C22.6173 2.43667 22.3633 2.91833 22.1093 3.15917L16.381 8.42917L21.8836 14.1808C22.0529 14.3792 22.1658 14.6908 21.9683 14.9033C21.7848 15.13 21.3616 15.1158 21.1781 14.9742L15.0123 9.69L11.9929 12.4525L8.98765 9.69L2.82187 14.9742C2.63845 15.1158 2.21517 15.13 2.03175 14.9033C1.83422 14.6908 1.94709 14.3792 2.1164 14.1808L7.61905 8.42917L1.89065 3.15917C1.63668 2.91833 1.38272 2.43667 1.84832 1.96917C2.31393 1.50167 2.79365 1.72833 3.18871 2.06833L11.9929 9.20833L20.8113 2.06833C21.2063 1.72833 21.6861 1.50167 22.1517 1.96917Z" fill="currentColor"></path>
                  </svg>
                </div>
                <h3 class="heading heading--small service-title">Email Marketing</h3>
                <p class="service-desc">Set up standing order in-app, and send money to any other bank account instantly</p>
              </div>
              <div class="service-item is-active">
                <div class="service-icon">
                  <svg width="30" height="30" viewbox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15 0C6.71484 0 0 5.0332 0 11.25C0 15.6445 3.36914 19.4414 8.26758 21.293L4.99805 30L14.4023 22.4766C14.6016 22.4824 14.7949 22.5 15 22.5C23.2852 22.5 30 17.4668 30 11.25C30 5.0332 23.2852 0 15 0ZM7.5 13.752C6.12305 13.752 4.99805 12.6328 4.99805 11.25C4.99805 9.86719 6.11719 8.74805 7.5 8.74805C8.87695 8.74805 10.002 9.86719 10.002 11.25C10.002 12.6328 8.87695 13.752 7.5 13.752ZM15 13.752C13.623 13.752 12.498 12.6328 12.498 11.25C12.498 9.86719 13.6172 8.74805 15 8.74805C16.377 8.74805 17.502 9.86719 17.502 11.25C17.502 12.6328 16.377 13.752 15 13.752ZM22.5 13.752C21.123 13.752 19.998 12.6328 19.998 11.25C19.998 9.86719 21.1172 8.74805 22.5 8.74805C23.877 8.74805 25.002 9.86719 25.002 11.25C25.002 12.6328 23.877 13.752 22.5 13.752Z" fill="currentColor"></path>
                  </svg>
                </div>
                <h3 class="heading heading--small service-title">SMS Marketing</h3>
                <p class="service-desc">Manage your account from your phone or computer. Do everything you need from the app.</p>
              </div>
              <div class="service-item">
                <div class="service-icon">
                  <svg width="26" height="26" viewbox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M23 3.00002C22.1875 2.18741 21.2228 1.54281 20.1612 1.10303C19.0995 0.663245 17.9616 0.43689 16.8125 0.43689C15.6634 0.43689 14.5255 0.663245 13.4638 1.10303C12.4022 1.54281 11.4375 2.18741 10.625 3.00002L0.849998 12.775C0.617185 13.0092 0.486507 13.326 0.486507 13.6563C0.486507 13.9865 0.617185 14.3033 0.849998 14.5375L5.275 18.9625L0.849998 23.375L2.625 25.15L7.0375 20.725L11.4625 25.15C11.6967 25.3828 12.0135 25.5135 12.3437 25.5135C12.674 25.5135 12.9908 25.3828 13.225 25.15L23 15.425C23.8201 14.6116 24.4711 13.6438 24.9153 12.5776C25.3595 11.5113 25.5882 10.3676 25.5882 9.21252C25.5882 8.05742 25.3595 6.91374 24.9153 5.84748C24.4711 4.78121 23.8201 3.81345 23 3.00002ZM19.625 6.37502C18.8506 5.63537 17.8209 5.22263 16.75 5.22263C15.6791 5.22263 14.6494 5.63537 13.875 6.37502L12.55 5.05002C13.6652 3.93863 15.1755 3.31458 16.75 3.31458C18.3245 3.31458 19.8347 3.93863 20.95 5.05002L19.625 6.37502Z" fill="currentColor"></path>
                  </svg>
                </div>
                <h3 class="heading heading--small service-title">Intellegent pop ups</h3>
                <p class="service-desc">Your bank account lets you easily lock your card in the app, and later reorder it witha tap of a button</p>
              </div>
            </div>
          </div>
        </section>
        <section class="tools">
          <div class="container">
            <div class="tools-main">
              <div class="tools-content">
                <h2 class="heading heading--big tools-heading">Join forces with your favorite business tools</h2>
                <p class="text tools-text">awareness through email became a must-have, and our ESP wasn&apos;t cutting</p><a class="button button--primary tools-button" href="#">See imagradtions</a>
              </div>
              <div class="tools-image"><img srcSet="./images/image-4.png 2x" alt="Tools Image"></div>
            </div>
          </div>
        </section>
        <section class="testimonial">
          <div class="container">
            <h2 class="heading heading--big testimonial-heading">See what our customer have to say</h2>
            <div class="testimonial-main">
              <div class="testimonial-image"><img srcSet="./images/image-5.png 2x" alt="Testimonial Image"></div>
              <div class="testimonial-content">
                <div class="testimonial-text">&quot;New 2022, we sent a few email blasts with MailChimp here and there, but it wasn&apos;t a focus. Once we shiftedaway from retail-only sales to eCornmerce soles, driving customer awareness through email became a must-have, and our ESP wasn&apos;t cutting</div>
                <h4 class="testimonial-author">Lello Mhoury. Crecter of Marketing</h4><img class="testimonial-star" srcSet="./images/image-star.png 2x" alt="Star Image">
                <p class="testimonial-rating">4.5 Rating Capture</p>
              </div>
            </div>
          </div>
        </section>
        <section class="library">
          <div class="container">
            <div class="library-header">
              <h2 class="heading heading--big">Sendlane Resource Library</h2>
              <p class="text">Cat in tuch with of our compare and take a personal tour of Sraklune</p>
            </div>
            <div class="library-tabs">
              <div class="library-tab is-active">Articles</div>
              <div class="library-tab">Ebooks</div>
              <div class="library-tab">Products</div>
            </div>
            <div class="library-list">
              <div class="library-item"><a class="library-image" href="#"><img src="./images/image-6.avif" alt="Library Image"></a><a class="library-category" href="#">Email Marketing</a>
                <h3><a class="library-title heading heading--small" href="#">Now to Grow Your Email List: 5 Email</a></h3>
              </div>
              <div class="library-item"><a class="library-image" href="#"><img src="./images/image-7.avif" alt="Library Image"></a><a class="library-category" href="#">Real-time anylitics</a>
                <h3><a class="library-title heading heading--small" href="#">Now to Grow Your Email List: 5 Email</a></h3>
              </div>
              <div class="library-item"><a class="library-image" href="#"><img src="./images/image-8.avif" alt="Library Image"></a><a class="library-category" href="#">Marketing Automation</a>
                <h3><a class="library-title heading heading--small" href="#">Now to Grow Your Email List: 5 Email</a></h3>
              </div>
            </div><a class="button button--primary library-more" href="#"><span>See Update</span><span class="icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24" stroke="currentColor" stroke-width="2" style="max-height: 24px">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg></span></a>
          </div>
        </section>
      </div>
      <footer class="footer">
        <div class="container">
          <div class="footer-header">
            <h2 class="heading heading--big">See it for yourself</h2>
            <p class="text">Cat in touch with of our compare and take a personal tour of Sraklune</p><a class="button button--primary footer-button" href="#">Request a demo</a>
          </div>
        </div>
      </footer>
    </div>
    <script src="./scripts/app.js"></script>
    <script src="./scripts/footer.js"></script>
  </body>
</html>