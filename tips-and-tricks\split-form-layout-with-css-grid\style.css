*,
*:before,
*:after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

img,
picture,
svg,
video {
  display: block;
  max-width: 100%;
}

input,
select,
textarea {
  background-color: transparent;
  outline: none;
}

button {
  cursor: pointer;
  background-color: transparent;
  outline: none;
  border: 0;
}

body {
  min-height: 100vh;
  font-weight: 400;
  font-size: 16px;
  line-height: 1;
  font-family: "DM Sans", sans-serif;
}

.form-layout {
  max-width: 500px;
  margin: 50px auto;
}

.form-group {
  margin-bottom: 40px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  grid-column-gap: 40px;
}

.form-input {
  padding: 10px;
  border: solid 1px #f001f0;
  outline: none;
}
