html {
  font-size: 62.5%;
}

* {
  box-sizing: border-box;
}

body {
  font-size: 1.6rem;
  font-family: "Poppins", sans-serif;
  font-weight: normal;
  background-color: #f3faff;
}

a {
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
}

.author {
  max-width: 1000px;
  margin: 2rem auto;
  display: flex;
  height: 40rem;
  border-radius: 1rem;
  overflow: hidden;

  &__image {
    width: 35%;
    height: 100%;
    object-fit: cover;
  }

  &__content {
    width: 65%;
    padding: 3rem;
    background-color: #eee;
  }

  &__name {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 3rem;
  }

  &__desc {
    font-size: 1.6rem;
    line-height: 1.8;
    margin-bottom: 3rem;
  }

  &__contact,
  &-social {
    display: flex;
    align-items: center;
  }

  &-social {
    &__item {
      margin-left: 1rem;
    }
  }
}

@media screen and (max-width: 767px) {
  .author {
    padding: 0 2rem;
    height: auto;
    flex-direction: column;

    &__image,
    &__content {
      width: 100%;
    }

    &__image {
      height: 20rem;
      border-radius: 1rem 1rem 0 0;
    }

    &__content {
      border-radius: 0 0 1rem 1rem;
    }
  }
}