:root {
  --primary-h: 248;
  --primary-s: 57%;
  --primary-l: 60%;
  --primary-color: #6e5fd3;
}

.host-heading {
  --mb: 20px;
}
.host-list {
  --gap: 12px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--gap);
}
.host-item {
  --gap: 6px;
  --avatar-size: 44px;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: var(--gap);
}
@media screen and (min-width: 768px) {
  .host-item {
    --gap: 7px;
    --avatar-size: 64px;
  }
}
.host-avatar {
  width: var(--avatar-size);
  height: var(--avatar-size);
  border-radius: 100rem;
  display: flex;
  align-items: flex-end;
  flex-direction: row;
  justify-content: center;
  gap: 0;
  overflow: hidden;
  position: relative;
  background-color: var(--color);
  box-shadow: 0 0 0 2px #fff, 0 0 0 3px var(--color);
}
.host-avatar img {
  position: relative;
  z-index: 2;
}
.host-name {
  font-size: 14px;
}
@media screen and (max-width: 767.98px) {
  .host {
    padding-bottom: 0;
  }
}
@container main-container (max-width: 420px) {
  .host-heading {
    --mb: 12px;
  }
  .host-list {
    display: grid;
    grid-auto-columns: 55px;
    grid-auto-flow: column;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    scroll-snap-stop: always;
    padding: 10px 0;
  }
  .host-list::-webkit-scrollbar {
    display: none;
    width: 0;
  }
  .host-list > * {
    scroll-snap-align: start;
  }
}

.trending {
  padding: 55px 0 36px;
  container-type: inline-size;
  container-name: trending;
}
.trending-heading {
  --mb: 11px;
}
.trending-banner {
  padding: 20px;
  border-radius: 10px;
  display: flex;
  align-items: flex-start;
  position: relative;
}
.trending-content {
  flex: 0 0 310px;
}
.trending-title {
  font-size: 22px;
  line-height: 1.45;
  margin-bottom: 11px;
}
.trending-title span {
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  background-image: linear-gradient(92deg, #415EF4 0.33%, #7141EF 28.14%);
}
.trending-podcast {
  font-size: 14px;
  color: var(--trending-podcast, #676a6f);
  margin-bottom: 4px;
}
.trending-author {
  font-size: 14px;
  color: var(--trending-author, #232333);
  margin-bottom: 20px;
}
.trending-author span {
  color: #676A6F;
}
.trending-image {
  position: absolute;
  max-width: 332px;
  bottom: 0;
  right: 15px;
}
.trending-vector, .trending-circle {
  position: absolute;
}
.trending-vector {
  right: 5%;
  top: 5%;
}
.trending-circle {
  right: 45%;
  bottom: 0;
}
@media screen and (max-width: 767px) {
  .trending {
    padding: 35px 0 25px;
  }
  .trending-circle {
    right: 15px;
  }
}
@container trending (max-width: 630px) {
  .trending-image {
    display: none;
  }
  .trending-content {
    flex: 1;
  }
}

.feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  container-type: inline-size;
  container-name: feed-header;
  margin-bottom: 40px;
}
.feed-tab {
  display: flex;
  white-space: nowrap;
}
.feed-tab-item {
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: #5e6064;
  padding: 10px;
  border-bottom: 1px solid #373A43;
}
.feed-tab-item.is-active {
  color: #6e5fd3;
  border-bottom-color: currentColor;
}
.feed-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--toggle-text, #5e6064);
  font-size: 14px;
  font-weight: 500;
}
@container feed-header (max-width: 470px) {
  .feed-toggle p {
    display: none;
  }
}
.feed-main {
  container-type: inline-size;
  container-name: feed-main;
}
.feed-item {
  border-radius: 10px;
  border: 1px solid var(--feed-border, #f0f3f6);
  margin-bottom: 30px;
}
.feed-item > * {
  padding: 20px;
}
.feed-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.feed-item-main {
  border-top: 1px solid var(--feed-border, #f0f3f6);
  border-bottom: 1px solid var(--feed-border, #f0f3f6);
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 20px;
}
.feed-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.feed-item-footer .button {
  font-size: 14px;
}
.feed-item-podcast {
  color: #A6A7B2;
  font-weight: 400;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1.5;
  font-size: 14px;
}
.feed-item-heading {
  line-height: 1.5;
  font-size: 14px;
}
.feed-item-time {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 12px;
}
.feed-item-time-item {
  display: flex;
  align-items: center;
  gap: 3px;
}
.feed-item-time-item span {
  font-size: 14px;
  color: #A6A7B2;
  line-height: 1.43;
}
.feed-item-desc {
  line-height: 1.5;
  font-weight: 400;
  font-size: 13px;
  margin-top: 16px;
}
.feed-image {
  flex: 0 0 152px;
  overflow: hidden;
  aspect-ratio: 1;
  width: 100%;
}
.feed-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}
.feed-author {
  display: flex;
  align-items: center;
  gap: 16px;
}
.feed-author-verify {
  position: absolute;
  z-index: 2;
  bottom: 0;
  right: 0;
}
.feed-author-avatar {
  width: 48px;
  height: 48px;
  flex: 0 0 48px;
  position: relative;
}
.feed-author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 100rem;
}
.feed-author-rating {
  display: flex;
  align-items: center;
  margin-top: 2px;
  margin-bottom: 4px;
  gap: 4px;
  color: #A6A7B2;
}
.feed-date {
  color: #A6A7B2;
  font-weight: 400;
}
.feed-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}
@container feed-main (min-width: 500px) {
  .feed-item-main {
    flex-direction: row;
  }
  .feed-author-header {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;
  }
  .feed-author-rating {
    gap: 10px;
    margin: 0;
  }
}

.top-podcast-images {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.top-podcast-images img {
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: 10px;
}

.follow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.follow-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.follow-user {
  display: flex;
  align-items: center;
  gap: 12px;
}
.follow-user-username {
  color: #A6A7B2;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.5;
  margin-top: 4px;
}
.follow-user-avatar {
  flex: 0 0 46px;
  aspect-ratio: 1;
  border-radius: 8px;
  box-shadow: 0 0 0 2px #fff, 0 0 0 3px #ffc0cb;
}
.follow-user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

.listen {
  display: flex;
  align-items: center;
  gap: 10px;
}
.listen-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.listen-meta {
  display: flex;
  align-items: center;
  gap: 16px;
}
.listen-meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #A6A7B2;
}
.listen-perspective {
  font-size: 13px;
  font-weight: 400;
  color: #A6A7B2;
}
.listen-title {
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 4px;
  margin-top: 4px;
}
.listen-image {
  flex: 0 0 100px;
  height: 67px;
  width: 100%;
}
.listen-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}
.listen-info {
  flex: 1;
}

.topic {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
}
.topic-list {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 12px 22px;
}
.topic-image {
  width: 100%;
  height: 86px;
  object-fit: cover;
  border-radius: inherit;
}
.topic-content {
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background-color: hsla(0, 0%, 0%, 0.7);
  color: #fff;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.topic-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
}
.topic-podcast {
  font-size: 13px;
  line-height: 1.5;
}

@container main-container (min-width: 790px) {
  .home-wrapper {
    display: grid;
    grid-template-columns: minmax(0, 2fr) minmax(0, 1fr);
    gap: 60px;
  }
}

/*# sourceMappingURL=home.css.map */
