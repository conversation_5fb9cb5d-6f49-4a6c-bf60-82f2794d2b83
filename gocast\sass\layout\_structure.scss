@use "../abstracts" as *;

.wrapper {
  max-width: 1440px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 256px minmax(0, 1fr);
  min-height: 100vh;
}

.main-container {
  padding: 37px 56px;
  container-type: inline-size;
  container-name: main-container;
}

.button-discover {
  width: 40px;
  height: 40px;
  border-radius: 100rem;
  background-color: var(--primary-color);
  position: fixed;
  right: 24px;
  bottom: 80px;
  z-index: 10;
  --p: 0;
  display: none;
}

@include screenMaxWidth(1279.98px) {
  .button-discover {
    display: flex;
  }

  .wrapper {
    padding-bottom: 80px;
    grid-template-columns: 1fr;
  }

  .main-container {
    padding: 0 24px;
  }
}