@use "sass:math";

*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
  font-family: "Roboto", sans-serif;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

// 16 / 9 --> x:y --> y / x * 100% = value ? (user for padding-bottom)

// variable
:root {
  --video-ratio: 9/16;
}

// mixin ratio
@mixin responsiveMedia($x, $y) {
  // unquote: remove string in sass, vd: unquote("John Doe") --> <PERSON>
  $bottom: unquote(math.div($y, $x) * 100 + "%");
  padding-bottom: $bottom;
}

.responsive-media {
  position: relative;
  height: 0;
  // padding-bottom: calc(var(--video-ratio) * 100%);
  @include responsiveMedia(16, 9);
}

.responsive-media>* {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}