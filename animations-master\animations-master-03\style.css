*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
  background-color: #131933;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.loading {
  margin: 50px auto;
  width: 70px;
  height: 70px;
  position: relative;
}
.loading div {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: rotate(calc(var(--value) * 45deg));
}
.loading div::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 10px;
  height: 10px;
  border: solid 10px #00aefd;
  border-radius: 50px;
  filter: hue-rotate(calc(var(--value) * 45deg));
  animation: loading 1s infinite;
  animation-delay: calc(var(--value) * 0.125s);
  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

@keyframes loading {
  to {
    transform: scale(0);
  }
}

/*# sourceMappingURL=style.css.map */
