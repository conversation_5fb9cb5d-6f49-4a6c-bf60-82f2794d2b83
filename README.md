# 🚀 Coding UI Collection

A curated collection of modern, responsive landing pages and UI components, built during my journey mastering HTML, CSS, and UI/UX design. This repository showcases best practices, modular architecture, and creative solutions for real-world web interfaces.

---

## 🌟 Featured Projects

| Project       | Description                   | Tech Stack          | Live Demo                                      |
| ------------- | ----------------------------- | ------------------- | ---------------------------------------------- |
| **Gocast**    | Podcast platform landing page | HTML, SCSS, Pug     | [Demo](https://gocast-olive.vercel.app)        |
| **LaslesVPN** | VPN service landing page      | HTML, SCSS, JS      | [Demo](https://lasles-vpn-xi-eight.vercel.app) |
| **Omnifood**  | Food delivery service website | HTML, CSS, JS       | [Demo](https://omnifood-lovat-one.vercel.app)  |
| **Pollock**   | Creative agency landing page  | HTML, SCSS, Pug, JS | [Demo](https://pollock-xi.vercel.app)          |

---

## 🛠️ Technologies & Concepts

- **HTML5, CSS3, SCSS**
- **JavaScript** (for interactivity)
- **Pug** (template engine)
- **Modern CSS**: Flexbox, Grid, Custom Properties, Animations, Media Queries
- **Component-based architecture**
- **Responsive design** (mobile-first)
- **Optimized assets**: SVG, images, custom icons

---

## 📁 Repository Structure

- `gocast/` – Podcast platform UI
- `lasles-vpn/` – VPN landing page
- `omnifood/` – Food delivery website
- `pollock/` – Creative agency landing page
- `components/` – Reusable UI components (checkboxes, dropdowns, etc.)
- `flexbox-master/`, `grid-master/`, `animations-master/` – Layout & animation demos
- `sample-ui/`, `tips-and-tricks/` – Mini UIs, CSS/HTML tips

---

## 📦 Getting Started

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-username/coding-ui.git
   cd coding-ui
   ```
2. **Open any project folder** in your browser (e.g., `gocast/index.html`) or use a local server for best results.
3. **For SCSS/Pug projects:** Compile SCSS to CSS and Pug to HTML using your preferred tools (e.g., VSCode extensions, CLI, or build tools).

---

## 🤝 Contributing

Contributions are welcome! Feel free to fork, submit pull requests, or suggest improvements. Please keep code clean, modular, and well-documented.

---

## 📚 Learning Resources

- [EvonDev Frontend YouTube](https://www.youtube.com/@evondevfrontend)
- [Udemy: Build Responsive Real-World Websites with HTML and CSS](https://www.udemy.com/course/design-and-develop-a-killer-website-with-html5-and-css3)
- [EvonHub](https://evonhub.dev/)

---

## 🎯 Purpose & Highlights

- Demonstrates modern UI/UX patterns and responsive layouts
- Clean, maintainable, and scalable codebase
- Modular SCSS and reusable components
- Real-world landing page examples
- Cross-browser compatibility

---

## 📱 Responsive Design

All UIs are fully responsive and tested on:

- Mobile devices
- Tablets
- Desktops
- Large screens

---

## 🎨 Design Assets

- Custom SVG icons
- Optimized images
- Modern typography & color schemes

---

> **Enjoy exploring and learning from this UI collection!**
