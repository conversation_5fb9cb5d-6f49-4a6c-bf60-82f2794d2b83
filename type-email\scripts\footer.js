// Footer functionality
document.addEventListener("DOMContentLoaded", function () {
  // Newsletter form validation and submission
  const newsletterForm = document.querySelector(".footer-newsletter-form");
  const emailInput = document.getElementById("newsletter-email");
  const emailError = document.getElementById("email-error");
  const submitButton = document.querySelector(".newsletter-button");

  if (newsletterForm && emailInput && emailError && submitButton) {
    // Email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    // Validate email function
    function validateEmail(email) {
      return emailRegex.test(email);
    }

    // Show error message
    function showError(message) {
      emailError.textContent = message;
      emailError.classList.add("show");
      emailInput.classList.add("error");
      emailInput.setAttribute("aria-invalid", "true");
    }

    // Hide error message
    function hideError() {
      emailError.textContent = "";
      emailError.classList.remove("show");
      emailInput.classList.remove("error");
      emailInput.setAttribute("aria-invalid", "false");
    }

    // Real-time validation
    emailInput.addEventListener("input", function () {
      const email = this.value.trim();

      if (email === "") {
        hideError();
        return;
      }

      if (!validateEmail(email)) {
        showError("Please enter a valid email address");
      } else {
        hideError();
      }
    });

    // Form submission
    newsletterForm.addEventListener("submit", function (e) {
      e.preventDefault();

      const email = emailInput.value.trim();

      // Validate email
      if (email === "") {
        showError("Email address is required");
        emailInput.focus();
        return;
      }

      if (!validateEmail(email)) {
        showError("Please enter a valid email address");
        emailInput.focus();
        return;
      }

      // Show loading state
      submitButton.classList.add("loading");
      submitButton.disabled = true;
      hideError();

      // Simulate API call (replace with actual API endpoint)
      setTimeout(() => {
        // Success simulation
        submitButton.classList.remove("loading");
        submitButton.disabled = false;

        // Show success message
        emailInput.value = "";
        emailInput.placeholder = "Thank you for subscribing!";
        emailInput.style.borderColor = "var(--primary-color)";

        // Reset after 3 seconds
        setTimeout(() => {
          emailInput.placeholder = "Enter your email";
          emailInput.style.borderColor = "";
        }, 3000);
      }, 2000);
    });

    // Accessibility: Handle Enter key on submit button
    submitButton.addEventListener("keydown", function (e) {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        this.click();
      }
    });
  }

  // Smooth scroll for footer links
  const footerLinks = document.querySelectorAll(
    ".footer-links a, .legal-links a"
  );

  footerLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      const href = this.getAttribute("href");

      // Only handle internal links (starting with #)
      if (href && href.startsWith("#")) {
        e.preventDefault();

        const targetId = href.substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });

          // Focus the target element for accessibility
          targetElement.focus();
        }
      }
    });
  });

  // Social links analytics tracking (optional)
  const socialLinks = document.querySelectorAll(".social-link");

  socialLinks.forEach((link) => {
    link.addEventListener("click", function () {
      const platform = this.getAttribute("aria-label")
        .split(" ")
        .pop()
        .toLowerCase();

      // Track social media clicks (replace with your analytics code)
      if (typeof gtag !== "undefined") {
        gtag("event", "social_click", {
          social_platform: platform,
          event_category: "footer",
          event_label: "social_media",
        });
      }
    });
  });

  // Keyboard navigation enhancement
  const focusableElements = document.querySelectorAll(
    '.footer a, .footer button, .footer input, .footer [tabindex]:not([tabindex="-1"])'
  );

  focusableElements.forEach((element, index) => {
    element.addEventListener("keydown", function (e) {
      // Handle arrow key navigation within footer
      if (e.key === "ArrowDown" || e.key === "ArrowRight") {
        e.preventDefault();
        const nextIndex = (index + 1) % focusableElements.length;
        focusableElements[nextIndex].focus();
      } else if (e.key === "ArrowUp" || e.key === "ArrowLeft") {
        e.preventDefault();
        const prevIndex =
          (index - 1 + focusableElements.length) % focusableElements.length;
        focusableElements[prevIndex].focus();
      }
    });
  });

  // Intersection Observer for footer animation
  const footer = document.querySelector(".footer");

  if (footer && "IntersectionObserver" in window) {
    const footerObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("footer-visible");
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      }
    );

    footerObserver.observe(footer);
  }

  // Handle reduced motion preference
  if (window.matchMedia("(prefers-reduced-motion: reduce)").matches) {
    // Disable animations for users who prefer reduced motion
    document.documentElement.style.setProperty("--animation-duration", "0s");
    document.documentElement.style.setProperty("--transition-duration", "0s");
  }

  // Handle high contrast mode
  if (window.matchMedia("(prefers-contrast: high)").matches) {
    document.body.classList.add("high-contrast");
  }

  // Handle color scheme preference
  const colorSchemeQuery = window.matchMedia("(prefers-color-scheme: dark)");

  function handleColorSchemeChange(e) {
    if (e.matches) {
      document.body.classList.add("dark-mode");
    } else {
      document.body.classList.remove("dark-mode");
    }
  }

  colorSchemeQuery.addListener(handleColorSchemeChange);
  handleColorSchemeChange(colorSchemeQuery);
});

// Utility function for debouncing
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Export for module systems (if needed)
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    debounce,
  };
}
