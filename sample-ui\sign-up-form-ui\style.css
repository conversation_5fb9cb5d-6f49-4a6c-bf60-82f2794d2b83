*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 62.5%;
}

body {
  line-height: 1.5;
  font-family: "Poppins", sans-serif;
  font-size: 1.6rem;
  background-color: #fbfbfb;
  padding: 5rem;
  min-height: 100vh;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.signup {
  width: 100%;
  height: 100%;
  background-color: #fafbff;
  padding: 5rem;
  border-radius: 10px;
  box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
}
.signup__content, .signup__image {
  width: calc(50% - 4rem);
}
.signup__heading {
  color: #291667;
  font-size: 4.5rem;
  font-weight: bold;
  margin-bottom: 4rem;
}
.signup__caption {
  color: #fc556f;
  font-weight: bold;
  position: relative;
  margin-bottom: 4rem;
  padding-left: 12rem;
}
.signup__caption::before {
  content: "";
  width: 10rem;
  height: 2px;
  background-color: #fc556f;
  position: absolute;
  top: 50%;
  left: 0;
  margin-top: -1px;
}
.signup-social {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-bottom: 5rem;
}
.signup-social__item {
  padding: 1.5rem 2rem;
  border-radius: 10px;
  border: solid 1px #eee;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  flex-wrap: nowrap;
  width: calc(50% - 2rem);
  color: #291667;
}
.signup-social__icon {
  margin-right: 2rem;
}
.signup-form__row {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-bottom: 2rem;
}
.signup-form__row .signup-form__group {
  width: calc(50% - 2rem);
}
.signup-form__label {
  color: #291667;
  display: inline-block;
  margin-bottom: 1rem;
  transform: translateX(1rem);
  cursor: pointer;
}
.signup-form__input {
  padding: 1.5rem 2rem;
  border-radius: 10px;
  border: solid 1px #eee;
  width: 100%;
  display: block;
  transition: all 0.25s linear;
}
.signup-form__input:focus {
  background-color: #fafbff;
  border-color: #fc556f;
}
.signup-form__input:focus + .signup-form__check {
  background-color: #fc556f;
  color: white;
}
.signup-form__validate {
  position: relative;
}
.signup-form__validate .signup-form__input {
  padding-right: 6rem;
}
.signup-form__check {
  width: 3rem;
  height: 3rem;
  background-color: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  position: absolute;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  transition: all 0.25s linear;
}
.signup-form__term {
  margin-bottom: 6rem;
}
.signup-form__term input {
  display: none;
}
.signup-form__term input:checked + label::before {
  background-color: #fc556f;
  border-color: #fc556f;
}
.signup-form__term label {
  display: inline-block;
  margin-top: 3rem;
  cursor: pointer;
  position: relative;
  padding-left: 3.5rem;
  transition: all 0.25s linear;
}
.signup-form__term label::before {
  content: "";
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  background-color: #eee;
  border: solid 1px #ccc;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  transition: all 0.25s linear;
}
.signup-form__submit {
  width: 8rem;
  height: 8rem;
  background-color: #fc556f;
  border: 0;
  border-radius: 3rem;
  color: white;
  font-size: 2.5rem;
  margin-bottom: 6.5rem;
  box-shadow: 0 10px 10px 0 rgba(252, 85, 111, 0.5);
  cursor: pointer;
}
.signup__already {
  color: #291667;
}
.signup__already a {
  color: #fc556f;
}
.signup__image {
  overflow: hidden;
  border-radius: 1rem;
}

@media screen and (max-width: 1349px) {
  .signup__content {
    width: calc(60% - 1rem);
  }
  .signup__image {
    width: calc(40% - 1rem);
  }
}
@media screen and (max-width: 1279px) {
  .signup__content {
    width: 100%;
  }
  .signup__image {
    display: none;
  }
}
@media screen and (max-width: 1279px) {
  body {
    padding: 2rem;
  }

  .signup {
    padding: 2rem;
  }
  .signup-social {
    flex-direction: column;
  }
  .signup-social__item {
    width: 100%;
    margin-bottom: 2rem;
  }
  .signup-form__row {
    flex-direction: column;
    margin-bottom: 0;
  }
  .signup-form__row .signup-form__group {
    width: 100%;
  }
  .signup-form__group {
    width: 100%;
    margin-bottom: 2rem;
  }
}

/*# sourceMappingURL=style.css.map */
