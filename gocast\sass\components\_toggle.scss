@use "../abstracts" as abst;

.toggle {
  &-main {
    @include abst.size(30px, 17px);
    @include abst.rounded("rounded-full");
    @include abst.bg(abst.$gray2);
    padding: 1.5px 2px;
    cursor: pointer;
    transition: all 0.2s linear;
  }

  input {
    display: none;

    &:checked+.toggle-main .toggle-spinner {
      transform: translateX(12px)
    }

    &:checked+.toggle-main {
      background-image: abst.$gradient;
    }
  }

  &-spinner {
    @include abst.rounded("rounded-full");
    background: #fff;
    width: 14px;
    aspect-ratio: 1;
    transition: all 0.2s linear;
  }
}