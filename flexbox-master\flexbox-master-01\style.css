html {
  font-size: 62.5%;
}

body {
  font-size: 1.6rem;
  font-family: "Poppins", sans-serif;
  font-weight: normal;
  background-color: #f3faff;
}

a {
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
}

.work {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}
.work__heading {
  color: #1a0a3b;
  font-weight: 500;
  font-size: 2.5rem;
  text-transform: capitalize;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  position: relative;
}
.work__heading::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 3.5rem;
  height: 6px;
  background-color: #00c899;
  border-radius: 4px;
}
.work__filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}
.work-tab {
  display: flex;
}
.work-tab__item {
  cursor: pointer;
  padding: 1rem 1.5rem;
  border: solid 1px #00c899;
  border-radius: 2rem 0 2rem 2rem;
  transition: all 0.2s ease;
}
.work-tab__item:not(:first-child) {
  margin-left: 1rem;
}
.work-tab__item:hover {
  color: white;
  background-color: #00c899;
}
.work__all {
  color: #00c899;
  padding: 5px 1rem;
  display: inline-block;
  border-radius: 1rem;
  overflow: hidden;
  background-color: rgba(0, 200, 153, 0.25);
  font-size: 1.4rem;
}
.work__list {
  display: flex;
  flex-wrap: wrap;
  margin-left: -1.5rem;
}
.work__item {
  width: calc(25% - 1.5rem);
  margin-left: 1.5rem;
  border-radius: 6px;
  overflow: hidden;
}
.work__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top;
}

@media screen and (max-width: 1023px) {
  .work__item {
    width: calc(50% - 1.5rem);
    margin-bottom: 1.5rem;
  }
  .work__list {
    margin-top: 2rem;
  }
}
@media screen and (max-width: 767px) {
  .work__item {
    width: 100%;
  }
  .work__filter {
    flex-direction: column;
  }
  .work-tab {
    flex-wrap: wrap;
    justify-content: center;
  }
  .work-tab__item {
    margin-bottom: 1.5rem;
  }
}
@media screen and (max-width: 1280px) and (min-width: 1024px) {
  .work {
    display: none;
  }
}

/*# sourceMappingURL=style.css.map */
