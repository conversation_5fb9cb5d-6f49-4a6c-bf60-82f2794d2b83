.header {
  background-image: linear-gradient(to bottom, var(--gray1e) 80%, var(--body-bg) 80%);
  margin-bottom: 140px;

  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 48px 0 83px;
  }

  &-logo {
    display: flex;
    align-items: center;
    gap: 0 15px;
    font-size: 25px;
    font-weight: 600;
  }

  &-logo-text {
    background: linear-gradient(93.52deg, #e6ff4b 8.71%, #00bd82 98.16%);
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text;
    display: inline-block;
  }

  &-login {
    display: inline-block;
    color: #fff;
    padding: 9px 25px;
    border-radius: 6px;
    line-height: 1.625;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      border-radius: inherit;
      background-color: var(--grayb5);
      opacity: 0.2;
    }
  }

  &-content {
    max-width: 873px;
    margin: 0 auto;
    text-align: center;
  }

  &-heading {
    font-size: 52px;
    line-height: calc(62 / 52);
    margin-bottom: 54px;
  }

  &-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 28px;
    margin-bottom: 88px;
  }

  &-works {
    display: flex;
    align-items: center;
    gap: 0 16px;
    color: #fff;
    font-size: 18px;
  }

  @media screen and (max-width: 1023.98px) {
    margin-bottom: 60px;

    &-top {
      justify-content: flex-start;
      padding: 25px 0 55px;
    }

    &-logo {
      font-size: 20px;
    }

    &-heading {
      font-size: 30px;
      line-height: 1.5;
      margin-bottom: 35px;
    }

    &-login {
      font-size: 12px;
      margin-left: auto;
      margin-right: 40px;
    }

    &-buttons {
      gap: 0 20px;
      margin-bottom: 64px;
    }

    &-works span {
      display: none;
    }
  }
}