.hero {
  padding-bottom: 100px;

  &-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0 40px;
    position: relative;
  }

  &-content {
    width: 100%;
    max-width: 555px;
  }

  &-heading {
    font-size: 50px;
    font-weight: 500;
    margin-bottom: 20px;
    line-height: calc(70 / 50);
    color: $heading-color;
  }

  &-desc {
    margin-bottom: 50px;
  }
}

@media screen and (min-width: 1280px) {
  .hero-image {
    position: absolute;
    top: 50%;
    transform: translate(40px, -50%);
    right: 0;
  }
}

@media screen and (max-width: 1023px) {
  .hero-heading {
    font-size: 35px;
  }

  .hero {
    padding-bottom: 50px;
  }
}

@media screen and (max-width: 767px) {
  .hero-container {
    flex-direction: column-reverse;
    gap: 50px 0;
  }

  .hero-desc {
    margin-bottom: 25px;
  }
}