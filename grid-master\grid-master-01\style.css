*,
*::before,
*::after {
  box-sizing: border-box;
}
* {
  margin: 0;
  padding: 0;
  font: inherit;
}
body {
  line-height: 1.5;
  font-family: "Roboto", sans-serif;
}
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}
input,
button,
textarea,
select {
  outline: none;
}
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.grid {
  display: grid;
  /* grid-template-column: Specifies the size of the columns and the number of columns in the grid layout */
  grid-template-columns: 100px 200px 100px 100px 100px 100px 100px 100px;

  grid-template-columns: 1fr 3fr 1fr 1fr 1fr 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  grid-template-columns: repeat(8, 1fr);
  grid-template-columns: repeat(4, 1fr);

  /* grid-template-rows: Specifies the size of the rows and the number of rows in the grid layout */
  /* grid-template-rows: 200px 400px; */

  /* grid-column-gap: Specifies the size of the gap between columns. */
  grid-column-gap: 10px;
  /* grid-row-gap: Specifies the size of the gap between rows. */
  grid-row-gap: 20px;
  /* grid-gap: grid-row-gap grid-column-gap */
  grid-gap: 30px 40px;

  /* minmax(minimum value, maximum value) */
  /* auto-fill: automatically fills the number of columns that are empty (it will only fill the number of columns, not the entire empty space), auto-fill will take the number of columns that are still empty */
  /* auto-fit: fills the entire empty space, when applying auto-fit it will usually take the maximum value */
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
}

.grid-item {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.grid2 {
  padding: 50px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 300px);
  grid-gap: 20px;
}

.grid2-item:first-child {
  /* grid-column: grid-column-start(first track line) / grid-column-end(last track line); */
  /* grid-column: specifies the size and position of the grid item according to the column in the grid layout */
  grid-column-start: 1;
  grid-column-end: 3;
  grid-column: 1 / 3;

  /* grid-row: grid-row-start / grid-row-end */
  /* grid-row: specifies the size and position of the grid item according to the row in the grid layout */
  grid-row-start: 1;
  grid-row-end: 3;
  grid-row: 1 / 3;
}

.grid2-item {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.grid2-item:nth-child(4) {
  grid-column: 1 / 2;
}

.grid2-item:nth-child(5) {
  grid-column: 2 / 3;
}

.grid2-item:last-child {
  /* last track line: index or -1 */
  /* span: how many rows or columns to occupy */
  grid-column: 3 / 5;
  grid-column: 3 / -1;
  grid-column: 3 / span 2;

  grid-row: 2 / 4;
  grid-row: 2 / -1;
  grid-row: 2 / span 2;
}

.grid3 {
  display: grid;
  padding: 40px;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 300px);
  grid-gap: 20px;

  /* grid-template-areas: specifies the areas in the grid
    *: we can name the elements in the grid by using the grid-area property and then reference the name in the grid-template-areas property
  */
  grid-template-areas:
    "h1 h1 h2 h3"
    "h1 h1 h6 h6"
    "h4 h5 h6 h6";

  /* 
    h1 h1 h2 h3
    h1 h1 h6 h6
    h4 h5 h6 h6
  */
}

.grid3-item {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.grid3-item:first-child {
  /* 
    grid-area: specifies the size and position of the element in the grid layout and is a shorthand property for the following properties: 
      - grid-row-start
      - grid-row-end
      - grid-column-start
      - grid-column-end

    The grid-area property can also be used to assign a name to an element. Named elements can then be referenced by the grid-template-areas property of the grid container.
  */
  grid-area: h1;
}

.grid3-item:nth-child(4) {
  grid-area: h4;
}

.grid3-item:nth-child(5) {
  grid-area: h5;
}

.grid3-item:last-child {
  grid-area: h6;
}

.grid4 {
  display: grid;
  padding: 40px;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: 350px 100px 350px;
  grid-gap: 20px;
}

.grid4-item {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  overflow: hidden;
}

.grid4-item:first-child {
  grid-column: 1 / 3;
  grid-column: 1 / span 2;
  grid-row: 1 / 2;
  grid-row: 1 / span 1;
}

.grid4-item:nth-child(2),
.grid4-item:nth-child(3) {
  grid-row: 1 / 3;
  grid-row: 1 / span 2;
}

.grid4-item:nth-child(4),
.grid4-item:nth-child(5) {
  grid-row: 2 / 4;
  grid-row: 2 / span 2;
}

.grid4-item:last-child {
  grid-column: 3 / -1;
  grid-column: 3 / -1;
}

.grid5 {
  display: grid;
  padding: 40px;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: 350px 100px 350px;
  grid-gap: 20px;
  grid-template-areas:
    "h1 h1 h2 h3"
    "h4 h5 h2 h3"
    "h4 h5 h6 h6";
}

.grid5-item {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  overflow: hidden;
}

.grid5-item:first-child {
  grid-area: h1;
}

.grid5-item:nth-child(2) {
  grid-area: h2;
}

.grid5-item:nth-child(3) {
  grid-area: h3;
}

.grid5-item:nth-child(4) {
  grid-area: h4;
}

.grid5-item:nth-child(5) {
  grid-area: h5;
}

.grid5-item:last-child {
  grid-area: h6;
}

.icon {
  width: 100px;
  height: 100px;
  background-color: red;
  border-radius: 10px;
  margin: 40px;

  /* display: flex;
  align-items: center;
  justify-content: center; */

  /* Center in css grid */
  display: grid;
  /* place-items: center = align-items: center; justify-content: center */
  place-items: center;
}
