.listen {
  display: flex;
  align-items: center;
  gap: 10px;

  &-list {
    display: flex;
    flex-direction: column;
    gap: 18px;
  }

  &-meta {
    display: flex;
    align-items: center;
    gap: 16px;

    &-item {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #A6A7B2;
    }
  }

  &-perspective {
    font-size: 13px;
    font-weight: 400;
    color: #A6A7B2;
  }

  &-title {
    font-weight: 500;
    line-height: 1.5;
    margin-bottom: 4px;
    margin-top: 4px;
  }

  &-image {
    flex: 0 0 100px;
    height: 67px;
    width: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px;
    }
  }

  &-info {
    flex: 1;
  }
}