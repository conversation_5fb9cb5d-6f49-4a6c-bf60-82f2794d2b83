.feature {
  padding: 102px 0 83px;

  &-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
  }

  &-image {
    flex-shrink: 0;
  }

  &-content {
    width: 100%;
    flex: 1;
    max-width: 548px;
  }

  &-title {
    margin-bottom: 18px;
  }

  &-text {
    margin-bottom: 28px;
  }

  &-link {
    margin-top: 51px;
    padding: 16px 58px;
  }

  @media screen and (max-width: 1023.98px) {
    padding: 0;

    &-main {
      flex-direction: column;
      gap: 64px 0;
    }
  }
}