.footer {
  background-color: $bg-gray;
  color: #fff;

  &-top {
    padding: 100px 0;
    display: flex;
    justify-content: space-between;
    gap: 10px;

    .logo {
      color: #fff;
    }
  }

  &-bottom {
    padding: 40px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    border-top: 1px solid rgba(123, 136, 168, 0.3);
  }

  &-social {
    display: flex;
    align-items: center;
    gap: 20px;

    &-item {
      background-color: $primary-color;
      width: 52px;
      height: 52px;
      border-radius: 100rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &-heading {
    font-size: 22px;
    line-height: calc(32 / 22);
    margin-bottom: 20px;
  }

  &-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  &-link {
    color: inherit;
    text-transform: uppercase;
  }

  &-column {
    flex-shrink: 0;

    &:last-child {
      max-width: 370px;
    }
  }

  .newsletter {
    &-desc {
      margin-bottom: 25px;
    }

    &-form {
      display: flex;
      height: 48px;
      width: 100%;
      gap: 6px;
    }

    &-input {
      all: unset;
      background-color: #fff;
      border-radius: 5px;
      font-size: 16px;
      padding: 0 18px;
      color: $text;
      flex: 1;
    }

    &-button {
      flex-direction: 0;
      padding: 0 24px;
    }

    &-warning {
      margin-top: 15px;
    }
  }

  @media screen and (max-width: 1023.98px) {
    &-top {
      padding: 50px 0;
      flex-direction: column;
      gap: 30px;
    }

    &-bottom {
      flex-direction: column;
      gap: 15px;
      text-align: center;
    }
  }
}