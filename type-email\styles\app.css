:root {
  --primary-font: "Sora", sans-serif;
  --body-bg: #fdf9f1;
  --gray1e: #1e1e1e;
  --grayb5: #b5b3af;
  --gray7d: #7d7d7d;
  --white: #fff;
  --body-size: 1536px;
  --container: 1100px;
  --gap: 15px;
  --primary-color: #00bd82;
  --primary-yellow: #e2f471;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

img,
picture,
svg,
video {
  display: block;
  max-width: 100%;
}

body {
  min-height: 100vh;
}

a {
  text-decoration: none;
}

ul,
ol {
  list-style: none;
}

body {
  background-color: var(--body-bg);
  font-family: var(--primary-font);
  font-weight: 400;
}

.wrapper {
  max-width: var(--body-size);
  margin: 0 auto;
}

.container {
  max-width: calc(var(--container) + var(--gap) * 2);
  padding: 0 var(--gap);
  margin: 0 auto;
}

.text-primary {
  background: linear-gradient(93.52deg, #e6ff4b 8.71%, #00bd82 98.16%);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}

.button {
  display: inline-block;
  color: #fff;
  border-radius: 10px;
  line-height: 1.5;
  cursor: pointer;
  border: 0;
  outline: none;
  padding: 15px 30px;
  font-size: 18px;
}
.button--primary {
  background-color: var(--primary-color);
}
@media screen and (max-width: 1023.98px) {
  .button {
    font-size: 14px;
    padding: 10px 20px;
  }
}

.heading {
  font-weight: 600;
  color: var(--gray1e);
}
.heading--big {
  font-size: 50px;
  line-height: 1.26;
}
.heading--normal {
  font-size: 40px;
  line-height: 1.25;
}
.heading--small {
  font-size: 22px;
  line-height: 1.4545454545;
}
@media screen and (max-width: 1023.98px) {
  .heading--big {
    font-size: 30px;
  }
  .heading--normal {
    font-size: 24px;
  }
  .heading--small {
    font-size: 20px;
  }
}

.text {
  font-size: 18px;
  line-height: 1.5555555556;
}
@media screen and (max-width: 1023.98px) {
  .text {
    font-size: 14px;
  }
}

a {
  text-decoration: none;
  color: inherit;
}

.header {
  background-image: linear-gradient(to bottom, var(--gray1e) 80%, var(--body-bg) 80%);
  margin-bottom: 140px;
}
.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 48px 0 83px;
}
.header-logo {
  display: flex;
  align-items: center;
  gap: 0 15px;
  font-size: 25px;
  font-weight: 600;
}
.header-logo-text {
  background: linear-gradient(93.52deg, #e6ff4b 8.71%, #00bd82 98.16%);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  display: inline-block;
}
.header-login {
  display: inline-block;
  color: #fff;
  padding: 9px 25px;
  border-radius: 6px;
  line-height: 1.625;
  position: relative;
}
.header-login::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background-color: var(--grayb5);
  opacity: 0.2;
}
.header-content {
  max-width: 873px;
  margin: 0 auto;
  text-align: center;
}
.header-heading {
  font-size: 52px;
  line-height: 1.1923076923;
  margin-bottom: 54px;
}
.header-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0 28px;
  margin-bottom: 88px;
}
.header-works {
  display: flex;
  align-items: center;
  gap: 0 16px;
  color: #fff;
  font-size: 18px;
}
@media screen and (max-width: 1023.98px) {
  .header {
    margin-bottom: 60px;
  }
  .header-top {
    justify-content: flex-start;
    padding: 25px 0 55px;
  }
  .header-logo {
    font-size: 20px;
  }
  .header-heading {
    font-size: 30px;
    line-height: 1.5;
    margin-bottom: 35px;
  }
  .header-login {
    font-size: 12px;
    margin-left: auto;
    margin-right: 40px;
  }
  .header-buttons {
    gap: 0 20px;
    margin-bottom: 64px;
  }
  .header-works span {
    display: none;
  }
}

.menu {
  display: flex;
  align-items: center;
  gap: 0 36px;
  padding: 0;
}
.menu-toggle {
  display: none;
}
.menu-toggle * {
  pointer-events: none;
}
.menu-link {
  color: var(--grayb5);
  position: relative;
  display: inline-block;
  transition: color 0.25s ease-out;
}
.menu-link::before {
  content: "";
  height: 2px;
  width: 0;
  background-image: linear-gradient(to right bottom, var(--primary-color), var(--primary-yellow));
  position: absolute;
  bottom: -2px;
  left: 0;
  transition: all 0.25s ease-in;
}
.menu-link:hover {
  color: #fff;
}
.menu-link:hover::before {
  width: 100%;
}
@media screen and (max-width: 1023.98px) {
  .menu {
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
    width: 300px;
    background-color: var(--gray1e);
    z-index: 9;
    flex-direction: column;
    align-items: stretch;
    transform: translate(150%, 0);
    transition: transform 0.25s ease-in;
  }
  .menu.is-show {
    transform: none;
  }
  .menu-item {
    margin-bottom: 15px;
  }
  .menu-link {
    display: block;
    padding: 15px;
  }
  .menu-link::before {
    display: none;
  }
  .menu-toggle {
    display: inline-block;
  }
}

.countdown {
  background-color: var(--primary-yellow);
  padding: 70px 0 47px;
}
.countdown-list {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  text-align: center;
}
.countdown-title {
  color: var(--gray1e);
  font-weight: 600;
  font-size: 44px;
  margin-bottom: 22px;
  line-height: 1.25;
}
.countdown-text {
  font-size: 18px;
  line-height: 1.5555555556;
}
@media screen and (max-width: 1023.98px) {
  .countdown {
    padding: 60px 0;
  }
  .countdown-list {
    flex-direction: column;
    row-gap: 64px;
    align-items: center;
  }
}

.newsletter {
  padding: 140px 0 66px 9.236%;
}
.newsletter-header {
  text-align: center;
  margin-bottom: 95px;
}
.newsletter-header .heading--big {
  max-width: 629px;
  margin: 0 auto 18px;
}
.newsletter-header .text {
  max-width: 475px;
  margin: 0 auto;
}
.newsletter-main {
  padding: 89px 0 72px;
}
.newsletter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  width: 100%;
}
.newsletter-content {
  flex: 1;
  max-width: 555px;
}
.newsletter-title {
  margin-bottom: 18px;
  max-width: 378px;
}
.newsletter-desc {
  margin-bottom: 37px;
  max-width: 508px;
}
.newsletter-list {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 74px;
}
.newsletter-icon {
  margin-bottom: 19px;
}
.newsletter-name {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: 9px;
}
.newsletter-text {
  margin-bottom: 28px;
  line-height: 1.625;
}
.newsletter-link {
  text-underline-offset: 2px;
}
.newsletter-link:hover {
  text-decoration: underline;
  text-decoration-color: var(--primary-color);
}
.newsletter-image {
  flex-shrink: 0;
}
@media screen and (max-width: 1023.98px) {
  .newsletter {
    padding: 60px 20px;
  }
  .newsletter-main {
    padding: 0;
  }
  .newsletter-header {
    margin-bottom: 60px;
  }
  .newsletter-container {
    flex-direction: column-reverse;
  }
  .newsletter-image {
    margin-bottom: 45px;
  }
  .newsletter-desc {
    margin-bottom: 65px;
  }
  .newsletter-list {
    flex-direction: column;
  }
}

.feature {
  padding: 102px 0 83px;
}
.feature-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}
.feature-image {
  flex-shrink: 0;
}
.feature-content {
  width: 100%;
  flex: 1;
  max-width: 548px;
}
.feature-title {
  margin-bottom: 18px;
}
.feature-text {
  margin-bottom: 28px;
}
.feature-link {
  margin-top: 51px;
  padding: 16px 58px;
}
@media screen and (max-width: 1023.98px) {
  .feature {
    padding: 0;
  }
  .feature-main {
    flex-direction: column;
    gap: 64px 0;
  }
}

.service {
  padding: 166px 0 140px;
}
.service-heading {
  text-align: center;
  margin-bottom: 58px;
}
.service-list {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 20px;
}
.service-item {
  padding: 43px 35px 63px;
  background-color: transparent;
  transition: all 0.2s linear;
}
.service-item.is-active, .service-item:hover {
  background-color: var(--body-bg);
  box-shadow: 0px 16px 250px 10px rgba(155, 155, 155, 0.25);
}
.service-item.is-active .service-icon, .service-item:hover .service-icon {
  background-color: var(--primary-color);
  color: #fff;
}
.service-icon {
  width: 66px;
  height: 66px;
  border-radius: 100rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 46px;
  background-color: rgba(0, 0, 0, 0.08);
  transition: all 0.2s linear;
}
.service-title {
  margin-bottom: 13px;
}
@media screen and (max-width: 1023.98px) {
  .service {
    padding: 60px 0;
  }
  .service-heading {
    margin-bottom: 35px;
  }
  .service-list {
    grid-template-columns: 100%;
    gap: 22px 0;
  }
  .service-item {
    padding: 43px 63px 63px 35px;
  }
}

.tools {
  padding: 59px 0 68px;
  background-color: var(--gray1e);
}
.tools-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}
.tools-image {
  transform: translateX(20%);
}
.tools-content {
  width: 100%;
  max-width: 584px;
  color: var(--grayb5);
}
.tools-heading {
  color: #fff;
  margin-bottom: 9px;
}
.tools-text {
  max-width: 362px;
  margin-bottom: 41px;
}
@media screen and (max-width: 1023.98px) {
  .tools {
    padding: 60px 0;
  }
  .tools-main {
    flex-direction: column;
    gap: 40px 0;
  }
  .tools-image {
    margin: 0 auto;
    transform: none;
  }
}

.testimonial {
  padding: 140px 0 52px;
}
.testimonial-heading {
  text-align: center;
  margin: 0 auto 76px;
  max-width: 596px;
}
.testimonial-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0 77px;
}
.testimonial-image {
  height: 470px;
  width: 100%;
  max-width: 502px;
}
.testimonial-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 3px solid #b0b0b0;
  border-radius: 42px;
}
.testimonial-content {
  flex: 1;
}
.testimonial-text {
  font-size: 23px;
  line-height: 1.7391304348;
  margin-bottom: 27px;
}
.testimonial-author {
  color: var(--gray7d);
  font-size: 18px;
  margin-bottom: 14px;
  font-weight: 300;
}
.testimonial-rating {
  font-size: 18px;
  margin-top: 10px;
}
@media screen and (max-width: 1023.98px) {
  .testimonial {
    padding: 60px 0;
  }
  .testimonial-heading {
    margin-bottom: 40px;
  }
  .testimonial-main {
    flex-direction: column;
    gap: 40px 0;
  }
  .testimonial-image {
    height: auto;
  }
  .testimonial-text {
    font-size: 16px;
  }
}

.library {
  padding: 154px 0 140px;
}
.library-header {
  text-align: center;
  margin-bottom: 32px;
}
.library-header .text {
  max-width: 327px;
  margin: 10px auto 0;
}
.library-tabs {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 86px;
}
.library-tab {
  cursor: pointer;
  font-size: 20px;
  font-weight: 600;
  padding: 12px 25px;
  border-radius: 12px;
  transition: all 0.2s linear;
}
.library-tab.is-active {
  color: var(--primary-yellow);
  background-color: var(--gray1e);
}
.library-list {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  grid-gap: 30px;
  max-width: 1010px;
  margin: 0 auto 38px;
}
.library-image {
  display: block;
  height: 227px;
  margin-bottom: 20px;
}
.library-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 22px;
}
.library-category {
  font-size: 18px;
  font-weight: 600;
  display: inline-block;
  color: var(--primary-color);
  margin-bottom: 8px;
}
.library-more {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0 24px;
  margin: 0 auto;
  white-space: nowrap;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}
.library-more .icon {
  width: 20px;
}
@media screen and (max-width: 1023.98px) {
  .library {
    padding: 60px 0;
  }
  .library-tabs {
    margin-bottom: 40px;
  }
  .library-tab {
    font-size: 14px;
  }
  .library-list {
    grid-template-columns: 100%;
  }
}

/*# sourceMappingURL=app.css.map */
