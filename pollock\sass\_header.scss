.header {
  background: $bg;
  padding: 16px 0 230px;
  position: relative;

  &-nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-shapes {
    pointer-events: none;
    user-select: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &-scroll {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 50%);
  }

  &-scroll-inner {
    border-radius: 100rem;
    width: 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $yellow;
    animation: bounced 1s linear infinite alternate;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  color: $text-heading;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.5;
  font-family: $font-secondary;
}

.menu {
  display: flex;
  align-items: center;
  gap: 30px;

  &-link {
    display: block;
    text-transform: uppercase;
    color: $text;
    font-size: 15px;
    font-weight: 500;
    font-family: $font-secondary;

    &:hover {
      color: $text-heading;
    }
  }
}

.tools {
  display: flex;
  align-items: center;
  gap: 20px;
}

.button-signup {
  padding: 0 38.5px;
  height: 48px;
}

@keyframes bounced {
  from {
    transform: translateY(-10px);
  }

  to {
    transform: translateY(10px);
  }
}

@media screen and (max-width: 1023.98px) {
  .header {
    padding-bottom: 80px;
  }

  .button-signup {
    display: none;
  }

  .menu {
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
    width: 300px;
    z-index: 20;
    background-color: $bg-gray;
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    transition: all 0.2s linear;
    transform: translateX(100%);

    &-link {
      padding: 10px;
      color: #fff;

      &:hover {
        color: $primary-color;
      }
    }
  }
}