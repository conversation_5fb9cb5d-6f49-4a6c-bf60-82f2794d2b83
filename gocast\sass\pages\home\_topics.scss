.topic {
  position: relative;
  border-radius: 10px;
  overflow: hidden;

  &-list {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 12px 22px;
  }

  &-image {
    width: 100%;
    height: 86px;
    object-fit: cover;
    border-radius: inherit;
  }

  &-content {
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background-color: hsla(0deg, 0%, 0%, 0.7);
    color: #fff;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  &-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
  }

  &-podcast {
    font-size: 13px;
    line-height: 1.5;
  }
}