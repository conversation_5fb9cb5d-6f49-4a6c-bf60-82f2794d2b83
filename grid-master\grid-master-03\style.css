*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
  font-family: sans-serif;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.post-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  grid-gap: 25px;
  grid-template-areas:
    "h1 h2 h3"
    "h1 h4 h5";
  padding: 25px;
}

.post-item:first-child {
  grid-area: h1;
}

.post-item:nth-child(2) {
  grid-area: h2;
}

.post-item:nth-child(3) {
  grid-area: h3;
}

.post-item:nth-child(4) {
  grid-area: h4;
}

.post-item:nth-child(5) {
  grid-area: h5;
}

.post-image {
  border-radius: 12px;
  width: 100%;
  object-fit: cover;
  height: 200px;
  flex-shrink: 0;
}

.post-content {
  font-weight: 300;
  padding: 25px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.post-image--large {
  height: auto;
}

.post-item {
  display: flex;
  flex-direction: column;
}

.post-title {
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 20px;
  color: #3f2a78;
}

.post-time {
  color: #ccc;
  text-transform: uppercase;
}
