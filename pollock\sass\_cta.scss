.cta {
  padding: 106px 0;
  background-color: $bg;
  position: relative;

  &-shapes {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    user-select: none;
  }

  &-heading {
    text-align: center;
    max-width: 870px;
    margin: 0 auto 30px;
  }

  &-caption {
    text-align: center;
    margin-bottom: 65px;
  }

  &-button {
    padding: 18px 32px;
    margin: 0 auto;
  }

  @media screen and (max-width: 767.98px) {
    padding: 80px 0;

    &-caption {
      margin-bottom: 25px;
    }
  }

}