@mixin bg($value: white) {
  background-color: $value;
}

@mixin placeholder($color: white) {
  &::-webkit-input-placeholder {
    color: $color;
  }

  &::-moz-input-placeholder {
    color: $color;
  }
}

@mixin size($width: 0, $height: $width) {
  width: $width;
  height: $height;
}

@mixin cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@mixin fw($value) {
  @if $value=="medium" {
    font-weight: 500;
  }

  @else if $value=="semibold" {
    font-weight: 600;
  }

  @else if $value=="bold" {
    font-weight: 700;
  }
}

@mixin flexBox($align: center, $justify: center, $gap: 0, $dir: row) {
  display: flex;
  align-items: $align;
  flex-direction: $dir;

  @if $justify {
    justify-content: $justify;
  }

  @if $gap {
    gap: $gap;
  }
}

@mixin rounded($value: "rounded") {
  @if $value =="rounded-lg" {
    border-radius: 8px;
  }

  @else if $value =="rounded-xl" {
    border-radius: 12px;
  }

  @else if $value =="rounded-full" {
    border-radius: 100rem;
  }

  @else if $value =="none" {
    border-radius: 0;
  }

  @else {
    border-radius: 4px;
  }
}

@mixin screenMinWidth($value) {
  @media screen and (min-width: $value) {
    @content;
  }
}

@mixin screenMaxWidth($value) {
  @media screen and (max-width: $value) {
    @content;
  }
}

@mixin textGradient() {
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}