@use "../../abstracts" as *;

.feed {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    container-type: inline-size;
    container-name: feed-header;
    margin-bottom: 40px;
  }

  &-tab {
    display: flex;
    white-space: nowrap;

    &-item {
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.43;
      color: $gray1;
      padding: 10px;
      border-bottom: 1px solid $gray37;
    }

    &-item.is-active {
      color: $primary-color;
      border-bottom-color: currentColor;
    }
  }

  &-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--toggle-text, $gray1);
    font-size: 14px;
    font-weight: 500;
  }

  @container feed-header (max-width: 470px) {
    &-toggle {
      p {
        display: none;
      }
    }
  }

  &-main {
    container-type: inline-size;
    container-name: feed-main;
  }

  &-item {
    border-radius: 10px;
    border: 1px solid var(--feed-border, #f0f3f6);
    margin-bottom: 30px;

    &>* {
      padding: 20px;
    }

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &-main {
      border-top: 1px solid var(--feed-border, #f0f3f6);
      border-bottom: 1px solid var(--feed-border, #f0f3f6);
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: 20px;
    }

    &-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .button {
        font-size: 14px;
      }
    }

    &-podcast {
      color: #A6A7B2;
      font-weight: 400;
      margin-bottom: 6px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 1.5;
      font-size: 14px;
    }

    &-heading {
      line-height: 1.5;
      font-size: 14px;
    }

    &-time {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 12px;

      &-item {
        display: flex;
        align-items: center;
        gap: 3px;

        span {
          font-size: 14px;
          color: #A6A7B2;
          line-height: 1.43;
        }
      }
    }

    &-desc {
      line-height: 1.5;
      font-weight: 400;
      font-size: 13px;
      margin-top: 16px;
    }
  }

  &-image {
    flex: 0 0 152px;
    overflow: hidden;
    aspect-ratio: 1;
    width: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px;
    }
  }

  &-author {
    display: flex;
    align-items: center;
    gap: 16px;

    &-verify {
      position: absolute;
      z-index: 2;
      bottom: 0;
      right: 0;
    }

    &-avatar {
      width: 48px;
      height: 48px;
      flex: 0 0 48px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 100rem;
      }
    }

    &-rating {
      display: flex;
      align-items: center;
      margin-top: 2px;
      margin-bottom: 4px;
      gap: 4px;
      color: #A6A7B2;
    }
  }

  &-date {
    color: #A6A7B2;
    font-weight: 400;
  }

  &-actions {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  @container feed-main (min-width: 500px) {
    &-item {
      &-main {
        flex-direction: row;
      }
    }

    &-author {
      &-header {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 4px;
      }

      &-rating {
        gap: 10px;
        margin: 0;
      }
    }
  }
}