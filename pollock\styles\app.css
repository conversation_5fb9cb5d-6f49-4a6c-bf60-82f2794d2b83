*,
*:before,
*:after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

img,
picture,
svg,
video {
  display: block;
  max-width: 100%;
}

body {
  min-height: 100vh;
}

a {
  text-decoration: none;
}

ul,
ol {
  list-style: none;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "DM Sans", sans-serif;
  font-size: 16px;
  color: #7b88a8;
  line-height: 1;
  background-color: #fff;
  font-weight: 400;
}

.wrapper {
  max-width: 1920px;
  margin: 0 auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.button {
  all: unset;
  color: #fff;
  border-radius: 5px;
  background-color: #4f77ff;
  cursor: pointer;
  font-size: 15px;
  line-height: 1.6;
  text-transform: uppercase;
  font-family: "Outfit";
  font-weight: 500;
  letter-spacing: -0.3px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
}

.heading {
  color: #2d3958;
  font-weight: 500;
  font-family: "Outfit";
}
.heading--biggest {
  font-size: 72px;
  line-height: 1.0833333333;
}
.heading--bigger {
  font-size: 64px;
  line-height: 1.125;
}
.heading--big {
  font-size: 48px;
  line-height: 1.1666666667;
}
.heading--large {
  font-size: 42px;
  line-height: 1.1904761905;
}
.heading--normal {
  font-size: 36px;
  line-height: 1.2777777778;
}
.heading--medium {
  font-size: 22px;
  line-height: 1.4545454545;
}
.heading--small {
  font-size: 18px;
  line-height: 1.5555555556;
}

.text {
  font-size: 18px;
  line-height: 1.5555555556;
}
.text--small {
  font-size: 16px;
  line-height: 1.625;
}
.text--xsmall {
  font-size: 14px;
  line-height: 1.7142857143;
}

.header {
  background: #f7f5f1;
  padding: 16px 0 230px;
  position: relative;
}
.header-nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-shapes {
  pointer-events: none;
  user-select: none;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.header-scroll {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translate(-50%, 50%);
}
.header-scroll-inner {
  border-radius: 100rem;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9c45c;
  animation: bounced 1s linear infinite alternate;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2d3958;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.5;
  font-family: "Outfit";
}

.menu {
  display: flex;
  align-items: center;
  gap: 30px;
}
.menu-link {
  display: block;
  text-transform: uppercase;
  color: #7b88a8;
  font-size: 15px;
  font-weight: 500;
  font-family: "Outfit";
}
.menu-link:hover {
  color: #2d3958;
}

.tools {
  display: flex;
  align-items: center;
  gap: 20px;
}

.button-signup {
  padding: 0 38.5px;
  height: 48px;
}

@keyframes bounced {
  from {
    transform: translateY(-10px);
  }
  to {
    transform: translateY(10px);
  }
}
@media screen and (max-width: 1023.98px) {
  .header {
    padding-bottom: 80px;
  }
  .button-signup {
    display: none;
  }
  .menu {
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
    width: 300px;
    z-index: 20;
    background-color: #292f3a;
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    transition: all 0.2s linear;
    transform: translateX(100%);
  }
  .menu-link {
    padding: 10px;
    color: #fff;
  }
  .menu-link:hover {
    color: #4f77ff;
  }
}
.hero {
  margin-top: 88px;
  position: relative;
}
.hero-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.hero-content {
  max-width: 770px;
  width: 100%;
}
.hero-heading {
  margin-bottom: 40px;
  word-wrap: break-word;
  line-height: 1.2;
}
.hero-heading span {
  background-image: linear-gradient(91.79deg, #857fff 32.21%, #ffc0ec 83.79%);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}
.hero-desc {
  margin-bottom: 70px;
}
.hero-links {
  display: flex;
  align-items: center;
  gap: 10px;
}
.hero-started {
  padding: 16px 32px;
}
.hero-play {
  display: flex;
  align-items: center;
  gap: 7px;
  text-transform: uppercase;
  font-weight: 500;
  font-family: "Outfit";
  color: #2d3958;
  font-size: 15px;
}
.hero-play-icon {
  border-radius: 100rem;
  width: 52px;
  height: 52px;
  background-color: #fff;
  box-shadow: 0px 5px 20px rgba(18, 2, 47, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

@media screen and (max-width: 767.98px) {
  .hero {
    margin-top: 40px;
  }
  .hero-container {
    flex-direction: column;
    gap: 30px;
  }
  .hero-desc {
    margin-bottom: 40px;
  }
  .hero-play {
    font-size: 13px;
  }
  .hero-play-icon {
    width: 40px;
    height: 40px;
  }
}
.services {
  padding: 150px 0 120px;
}
.services-heading {
  text-align: center;
  max-width: 970px;
  margin: 0 auto 30px;
}
.services-caption {
  text-align: center;
  font-size: 18px;
  letter-spacing: -0.3px;
  line-height: 1.5555555556;
  margin-bottom: 80px;
}
.services-list {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 30px;
  margin-bottom: 70px;
}
.services-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px 35px 60px;
  background-color: #fff;
  border: 1px solid rgba(97, 119, 152, 0.2);
  border-radius: 20px;
  transition: all 0.2s linear;
}
.services-item:hover {
  border-color: transparent;
  box-shadow: 0px 20px 50px rgba(79, 119, 255, 0.2);
}
.services-title {
  margin: 22px 0 40px;
  color: #120f1f;
  font-family: "Outfit";
  font-weight: 400;
  font-size: 22px;
  line-height: 1.4545454545;
}
.services-desc {
  line-height: 1.625;
}
.services-link {
  padding: 0 32px;
  height: 60px;
  margin: 0 auto;
}

@media screen and (max-width: 1023.98px) {
  .services {
    padding: 80px 0;
  }
}
@media screen and (max-width: 767.98px) {
  .services-list {
    grid-template-columns: 1fr;
  }
  .services-title {
    margin: 10px 0 20px;
  }
}
.logos {
  padding-bottom: 120px;
}
.logos-heading {
  color: #000;
  text-align: center;
  margin-bottom: 80px;
}
.logos-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}
@media screen and (max-width: 767.98px) {
  .logos {
    padding-bottom: 80px;
  }
  .logos-heading {
    margin-bottom: 40px;
  }
  .logos-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}

.about {
  background-color: #f7f5f1;
  padding: 120px 0;
}
.about-heading {
  text-align: center;
  margin-bottom: 30px;
}
.about-caption {
  text-align: center;
  margin-bottom: 120px;
}
.about-sections {
  display: flex;
  flex-direction: column;
  gap: 150px;
}
.about-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.about-section:nth-child(even) {
  flex-direction: row-reverse;
}
.about-section-content {
  width: 100%;
  max-width: 570px;
}
.about-section-heading {
  font-size: 36px;
  line-height: 1.2777777778;
  margin-bottom: 30px;
}
.about-section-button {
  margin-top: 30px;
  padding: 18px 32px;
}
.about-items {
  margin-top: 50px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}
.about-item {
  display: flex;
  align-items: flex-start;
  gap: 13px;
}
.about-item-title {
  font-size: 18px;
  margin-bottom: 10px;
  line-height: 1.5555555556;
}
.about-item-desc {
  line-height: 1.625;
}
@media screen and (max-width: 767.98px) {
  .about {
    padding: 80px 0;
  }
  .about-caption {
    margin-bottom: 80px;
  }
  .about-sections {
    gap: 40px;
  }
  .about-section {
    flex-direction: column;
    gap: 20px;
  }
  .about-section:nth-child(even) {
    flex-direction: column;
  }
}

.portfolio {
  padding: 120px 0;
}
.portfolio-heading {
  text-align: center;
  max-width: 970px;
  margin: 0 auto 100px;
}
.portfolio-item {
  display: flex;
  flex-direction: column;
  gap: 30px;
  color: #4f77ff;
  font-size: 22px;
  line-height: 1.4545454545;
}
.portfolio-item:first-child {
  grid-column: 1/span 7;
}
.portfolio-item:nth-child(2) {
  grid-column: 8/-1;
}
.portfolio-item:nth-child(3) {
  grid-column: 1/span 5;
}
.portfolio-item:last-child {
  grid-column: 6/-1;
}
.portfolio-list {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 50px 30px;
}
@media screen and (max-width: 767.98px) {
  .portfolio {
    padding: 80px 0;
  }
  .portfolio-heading {
    margin-bottom: 40px;
  }
  .portfolio-list {
    gap: 30px 15px;
  }
  .portfolio-item {
    gap: 15px;
    font-size: 16px;
  }
}

.cta {
  padding: 106px 0;
  background-color: #f7f5f1;
  position: relative;
}
.cta-shapes {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  user-select: none;
}
.cta-heading {
  text-align: center;
  max-width: 870px;
  margin: 0 auto 30px;
}
.cta-caption {
  text-align: center;
  margin-bottom: 65px;
}
.cta-button {
  padding: 18px 32px;
  margin: 0 auto;
}
@media screen and (max-width: 767.98px) {
  .cta {
    padding: 80px 0;
  }
  .cta-caption {
    margin-bottom: 25px;
  }
}

.footer {
  background-color: #292f3a;
  color: #fff;
}
.footer-top {
  padding: 100px 0;
  display: flex;
  justify-content: space-between;
  gap: 10px;
}
.footer-top .logo {
  color: #fff;
}
.footer-bottom {
  padding: 40px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  border-top: 1px solid rgba(123, 136, 168, 0.3);
}
.footer-social {
  display: flex;
  align-items: center;
  gap: 20px;
}
.footer-social-item {
  background-color: #4f77ff;
  width: 52px;
  height: 52px;
  border-radius: 100rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer-heading {
  font-size: 22px;
  line-height: 1.4545454545;
  margin-bottom: 20px;
}
.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.footer-link {
  color: inherit;
  text-transform: uppercase;
}
.footer-column {
  flex-shrink: 0;
}
.footer-column:last-child {
  max-width: 370px;
}
.footer .newsletter-desc {
  margin-bottom: 25px;
}
.footer .newsletter-form {
  display: flex;
  height: 48px;
  width: 100%;
  gap: 6px;
}
.footer .newsletter-input {
  all: unset;
  background-color: #fff;
  border-radius: 5px;
  font-size: 16px;
  padding: 0 18px;
  color: #7b88a8;
  flex: 1;
}
.footer .newsletter-button {
  flex-direction: 0;
  padding: 0 24px;
}
.footer .newsletter-warning {
  margin-top: 15px;
}
@media screen and (max-width: 1023.98px) {
  .footer-top {
    padding: 50px 0;
    flex-direction: column;
    gap: 30px;
  }
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}

/*# sourceMappingURL=app.css.map */
