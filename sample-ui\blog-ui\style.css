html {
  font-size: 62.5%;
}

body {
  font-size: 1.6rem;
  font-family: "Poppins", sans-serif;
  font-weight: normal;
  background-color: #f3f3f3;
  padding: 2rem;
}

a {
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
}

.post {
  padding: 2rem;
  background-color: white;
  border-radius: 1rem;
  max-width: 100rem;
  margin: 0.3rem auto;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: wrap;
}
.post__left, .post__right {
  width: calc(50% - 1rem);
}
.post__media {
  height: 40.5rem;
  margin-bottom: 3.5rem;
  position: relative;
}
.post__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 1rem;
  overflow: hidden;
}
.post__image--small {
  width: 15rem;
  height: 10rem;
  margin-right: 1.5rem;
  flex-shrink: 0;
}
.post__category {
  display: inline-block;
  padding: 1rem;
  background-image: linear-gradient(to right, #e04e63, #fd8783);
  border-radius: 2rem;
  font-size: 1.5rem;
  color: white;
  cursor: pointer;
  position: absolute;
  top: 2rem;
  left: 2rem;
  z-index: 2;
}
.post__icon {
  width: 5rem;
  height: 5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  flex-wrap: nowrap;
  color: white;
  background-image: linear-gradient(to right, #e04e63, #fd8783);
  border-radius: 5rem;
  cursor: pointer;
  position: absolute;
  bottom: 0;
  right: 2rem;
  transform: translateY(50%);
  z-index: 2;
}
.post-author {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
  flex-wrap: nowrap;
  color: #999;
  font-size: 1.4rem;
  margin-bottom: 2rem;
}
.post-author__avatar {
  width: 5rem;
  height: 5rem;
  border-radius: 5rem;
  object-fit: cover;
  margin-right: 1rem;
}
.post-author__name {
  position: relative;
  margin-right: 2.5rem;
  padding-right: 2.5rem;
}
.post-author__name::before {
  content: "";
  width: 4px;
  height: 4px;
  background-color: #e04e63;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
  flex-wrap: nowrap;
  border-radius: 5rem;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  z-index: 2;
}
.post__date {
  color: #999;
  font-size: 1.4rem;
}
.post__title {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 1.4rem;
  line-height: 1.6;
  color: #333;
}
.post__title--small {
  font-size: 1.5rem;
  margin-bottom: 0;
}
.post__link {
  color: inherit;
}
.post__desc {
  color: #999;
  font-size: 1.6rem;
  line-height: 1.6;
}
.post__item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
  flex-wrap: nowrap;
}
.post__item:not(:last-child) {
  margin-bottom: 3rem;
  padding-bottom: 3rem;
  border-bottom: solid 3px #eee;
}
.post__content {
  width: 100%;
  flex-grow: 1;
}

@media screen and (max-width: 767px) {
  .post {
    margin: 0;
  }
  .post__left, .post__right {
    width: 100%;
  }
  .post__left {
    margin-bottom: 2.5rem;
  }
  .post__image--small {
    width: 10rem;
    height: 10rem;
  }
  .post__title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
  }
  .post__title--small {
    margin-bottom: 1.5rem;
  }
}

/*# sourceMappingURL=style.css.map */
