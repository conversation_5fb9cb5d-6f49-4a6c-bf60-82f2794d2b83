.hero {
  margin-top: 88px;
  position: relative;

  &-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-content {
    max-width: 770px;
    width: 100%;
  }

  &-heading {
    margin-bottom: 40px;
    word-wrap: break-word;
    line-height: 1.2;

    span {
      background-image: linear-gradient(91.79deg, #857fff 32.21%, #ffc0ec 83.79%);
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text;
    }
  }

  &-desc {
    margin-bottom: 70px;
  }

  &-links {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  &-started {
    padding: 16px 32px;
  }

  &-play {
    display: flex;
    align-items: center;
    gap: 7px;
    text-transform: uppercase;
    font-weight: 500;
    font-family: $font-secondary;
    color: $text-heading;
    font-size: 15px;
  }

  &-play-icon {
    border-radius: 100rem;
    width: 52px;
    height: 52px;
    background-color: #fff;
    box-shadow: 0px 5px 20px rgba(18, 2, 47, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@media screen and (max-width: 767.98px) {
  .hero {
    margin-top: 40px;

    &-container {
      flex-direction: column;
      gap: 30px;
    }

    &-desc {
      margin-bottom: 40px;
    }

    &-play {
      font-size: 13px;

      &-icon {
        width: 40px;
        height: 40px;
      }
    }
  }
}