@use "../../abstracts" as abst;

.trending {
  padding: 55px 0 36px;
  container-type: inline-size;
  container-name: trending;

  &-heading {
    --mb: 11px;
  }

  &-banner {
    padding: 20px;
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    position: relative;
  }

  &-content {
    flex: 0 0 310px;
  }

  &-title {
    font-size: 22px;
    line-height: 1.45;
    margin-bottom: 11px;

    span {
      @include abst.textGradient();
      background-image: abst.$gradient;
    }
  }

  &-podcast {
    font-size: 14px;
    color: var(--trending-podcast, #676a6f);
    margin-bottom: 4px;
  }

  &-author {
    font-size: 14px;
    color: var(--trending-author, abst.$primary-black);
    margin-bottom: 20px;

    span {
      color: abst.$gray-natural;
    }
  }

  &-image {
    position: absolute;
    max-width: 332px;
    bottom: 0;
    right: 15px;
  }

  &-vector,
  &-circle {
    position: absolute;
  }

  &-vector {
    right: 5%;
    top: 5%;
  }

  &-circle {
    right: 45%;
    bottom: 0;
  }

  @include abst.screenMaxWidth(767px) {
    padding: 35px 0 25px;

    &-circle {
      right: 15px;
    }
  }

  @container trending (max-width: 630px) {
    &-image {
      display: none;
    }

    &-content {
      flex: 1;
    }
  }
}