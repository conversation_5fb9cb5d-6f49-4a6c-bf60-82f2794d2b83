*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
  height: 100vh;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: brightness(3);
  -webkit-backdrop-filter: brightness(3);
  backdrop-filter: grayscale(10);
  -webkit-backdrop-filter: grayscale(10);
  backdrop-filter: opacity(0.5);
  backdrop-filter: blur(2px);
}

.content {
  width: 70vh;
  height: 70vh;
  padding: 50px;
  background-color: #fff;
}

:root {
  --ratio: 16/9;
}

.media {
  height: 0;
  position: relative;
  padding-bottom: 56.25%;
  padding-bottom: calc(100% / (var(--ratio)));
}
.media iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/*# sourceMappingURL=style.css.map */
