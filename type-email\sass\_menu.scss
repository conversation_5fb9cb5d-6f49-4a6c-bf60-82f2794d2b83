.menu {
  display: flex;
  align-items: center;
  gap: 0 36px;
  padding: 0;

  &-toggle {
    display: none;

    * {
      pointer-events: none;
    }
  }

  &-link {
    color: var(--grayb5);
    position: relative;
    display: inline-block;
    transition: color 0.25s ease-out;

    &::before {
      content: "";
      height: 2px;
      width: 0;
      background-image: linear-gradient(to right bottom, var(--primary-color), var(--primary-yellow));
      position: absolute;
      bottom: -2px;
      left: 0;
      transition: all 0.25s ease-in;
    }

    &:hover {
      color: #fff;

      &::before {
        width: 100%;
      }
    }
  }

  @media screen and (max-width: 1023.98px) {
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
    width: 300px;
    background-color: var(--gray1e);
    z-index: 9;
    flex-direction: column;
    align-items: stretch;
    transform: translate(150%, 0);
    transition: transform 0.25s ease-in;

    &.is-show {
      transform: none;
    }

    &-item {
      margin-bottom: 15px;
    }

    &-link {
      display: block;
      padding: 15px;

      &::before {
        display: none;
      }
    }

    &-toggle {
      display: inline-block;
    }
  }
}