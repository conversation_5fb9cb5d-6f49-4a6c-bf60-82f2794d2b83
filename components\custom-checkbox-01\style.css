*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
}

body {
  line-height: 1.5;
  font-family: "Poppins", sans-serif;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.checkbox {
  margin: 100px;
}
.checkbox__input {
  display: none;
}
.checkbox__input:checked + .checkbox__label::after {
  opacity: 1;
  visibility: visible;
}
.checkbox__label {
  position: relative;
  cursor: pointer;
  padding-left: 50px;
}
.checkbox__label::before {
  content: "";
  width: 40px;
  height: 40px;
  background-color: #ffc56f;
  border-radius: 10px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.checkbox__label::after {
  content: "";
  width: 20px;
  height: 10px;
  position: absolute;
  left: 10px;
  top: 0;
  transform: rotate(-45deg);
  border-bottom: solid 3px #fff;
  border-left: solid 3px #fff;
  opacity: 0;
  visibility: hidden;
  transition: 0.25s linear;
}

/*# sourceMappingURL=style.css.map */
