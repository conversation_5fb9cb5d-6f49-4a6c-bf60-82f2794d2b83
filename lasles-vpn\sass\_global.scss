body {
  line-height: 1;
  font-family: $font;
  color: $text-color;
  font-weight: 400;
  background-color: white;
}

button {
  cursor: pointer;
  outline: none;
  border: none;
  font-family: $font;
}

.wrapper {
  max-width: 1440px;
  margin: 0 auto;
}

.container {
  max-width: 1170px;
  margin: 0 auto;
  padding: 0 15px;
}

.button {
  display: inline-block;
  padding: 13px 45px;
  border-radius: 100rem;
  font-family: $font;
  line-height: 1;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  border: none;
  text-align: center;
  white-space: nowrap;

  &--primary {
    font-weight: 700;
    background-color: $primary-color;
    color: white;
  }

  &--outline {
    background-color: transparent;
    color: $primary-color;
    font-weight: 500;
    border: solid 1px;
  }

  &--rounded {
    border-radius: 10px;
    padding: 17px 65px;
  }

  &--shadow {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 50%;
      width: 90%;
      height: 100%;
      transform: translate(-50%, 50%);
      background-color: rgba(245, 56, 56, 0.35);
      filter: blur(54px);
      border-radius: inherit;
    }
  }
}

.boxed {
  max-width: 550px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

@media screen and (max-width: 767px) {
  .button {
    padding: 13px 26px;
  }
}