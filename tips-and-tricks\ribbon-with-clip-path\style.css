*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.polygon {
  width: 100px;
  height: 200px;
  background-color: #f001f0;
  border-radius: 5px;
  margin: 20px auto;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 50% 75%, 0 100%);
}
