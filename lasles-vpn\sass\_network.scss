.network {
  &-heading {
    max-width: 380px;
    margin-left: auto;
    margin-right: auto;
  }

  &-image {
    margin-top: 155px;

    img {
      margin: 0 auto;
    }
  }
}

.partner {
  margin-bottom: 50px;

  &-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 60px;
  }
}

@media screen and (max-width: 1023px) {
  .network-image {
    margin-top: 50px;
  }

  .partner-container {
    gap: 0 20px;
  }
}

@media screen and (max-width: 767px) {
  .partner {
    margin-bottom: 30px;
  }
}