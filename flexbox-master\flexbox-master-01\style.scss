$green-color: #00c899;

@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

html {
  font-size: 62.5%;
}

body {
  font-size: 1.6rem;
  font-family: "Poppins", sans-serif;
  font-weight: normal;
  background-color: #f3faff;
}

a {
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
}

.work {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  &__heading {
    color: #1a0a3b;
    font-weight: 500;
    font-size: 2.5rem;
    text-transform: capitalize;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      @include size(3.5rem, 6px);
      background-color: $green-color;
      border-radius: 4px;
    }
  }

  &__filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  &-tab {
    display: flex;

    &__item {
      cursor: pointer;
      padding: 1rem 1.5rem;
      border: solid 1px $green-color;
      border-radius: 2rem 0 2rem 2rem;
      transition: all 0.2s ease;

      &:not(:first-child) {
        margin-left: 1rem;
      }

      &:hover {
        color: white;
        background-color: $green-color;
      }
    }
  }

  &__all {
    color: $green-color;
    padding: 5px 1rem;
    display: inline-block;
    border-radius: 1rem;
    overflow: hidden;
    background-color: rgba($green-color, 0.25);
    font-size: 1.4rem;
  }

  &__list {
    display: flex;
    flex-wrap: wrap;
    margin-left: -1.5rem;
  }

  &__item {
    width: calc(25% - 1.5rem);
    margin-left: 1.5rem;
    border-radius: 6px;
    overflow: hidden;
  }

  &__image {
    @include size(100%);
    object-fit: cover;
    object-position: top;
  }
}

@media screen and (max-width: 1023px) {
  .work {
    &__item {
      width: calc(50% - 1.5rem);
      margin-bottom: 1.5rem;
    }

    &__list {
      margin-top: 2rem;
    }
  }
}

@media screen and (max-width: 767px) {
  .work {
    &__item {
      width: 100%;
    }

    &__filter {
      flex-direction: column;
    }

    &-tab {
      flex-wrap: wrap;
      justify-content: center;

      &__item {
        margin-bottom: 1.5rem;
      }
    }
  }
}

@media screen and (max-width: 1280px) and (min-width: 1024px) {
  .work {
    display: none;
  }
}