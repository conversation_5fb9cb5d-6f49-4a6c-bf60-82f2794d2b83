.library {
  padding: 154px 0 140px;

  &-header {
    text-align: center;
    margin-bottom: 32px;

    .text {
      max-width: 327px;
      margin: 10px auto 0;
    }
  }

  &-tabs {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 86px;
  }

  &-tab {
    cursor: pointer;
    font-size: 20px;
    font-weight: 600;
    padding: 12px 25px;
    border-radius: 12px;
    transition: all 0.2s linear;

    &.is-active {
      color: var(--primary-yellow);
      background-color: var(--gray1e);
    }
  }

  &-list {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    grid-gap: 30px;
    max-width: 1010px;
    margin: 0 auto 38px;
  }

  &-image {
    display: block;
    height: 227px;
    margin-bottom: 20px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 22px;
    }
  }

  &-category {
    font-size: 18px;
    font-weight: 600;
    display: inline-block;
    color: var(--primary-color);
    margin-bottom: 8px;
  }

  &-more {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0 24px;
    margin: 0 auto;
    white-space: nowrap;
    position: relative;
    left: 50%;
    transform: translateX(-50%);

    .icon {
      width: 20px;
    }
  }

  @media screen and (max-width: 1023.98px) {
    padding: 60px 0;

    &-tabs {
      margin-bottom: 40px;
    }

    &-tab {
      font-size: 14px;
    }

    &-list {
      grid-template-columns: 100%;
    }
  }
}