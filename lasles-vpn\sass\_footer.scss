.footer {
  padding-bottom: 100px;
  background-color: $gray-light-color;
  padding-top: 180px;

  &-container {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr;
    grid-gap: 0 126px;
  }

  &-logo {
    margin-bottom: 20px;
    display: inline-block;
  }

  &-desc {
    margin-bottom: 30px;
  }

  &-heading {
    margin-bottom: 20px;
  }

  &-links {
    list-style-type: none;
  }

  &-item {
    margin-bottom: 10px;
  }

  &-link {
    color: inherit;
    line-height: calc(30 / 16);
  }

  .social {
    display: flex;
    align-items: center;
    gap: 0 20px;
  }

  .copyright {
    color: #afb5c0;
  }
}

@media screen and (max-width: 1023px) {
  .footer {
    padding: 50px 0;
  }

  .footer-container {
    gap: 0 30px;
  }
}

@media screen and (max-width: 767px) {
  .footer-container {
    grid-template-columns: 100%;
    grid-gap: 30px 0;
  }
}