.about {
  background-color: $bg;
  padding: 120px 0;

  &-heading {
    text-align: center;
    margin-bottom: 30px;
  }

  &-caption {
    text-align: center;
    margin-bottom: 120px;
  }

  &-sections {
    display: flex;
    flex-direction: column;
    gap: 150px;
  }

  &-section {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:nth-child(even) {
      flex-direction: row-reverse;
    }

    &-content {
      width: 100%;
      max-width: 570px;
    }

    &-heading {
      font-size: 36px;
      line-height: calc(46 / 36);
      margin-bottom: 30px;
    }

    &-button {
      margin-top: 30px;
      padding: 18px 32px;
    }
  }

  &-items {
    margin-top: 50px;
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  &-item {
    display: flex;
    align-items: flex-start;
    gap: 13px;

    &-title {
      font-size: 18px;
      margin-bottom: 10px;
      line-height: calc(28 / 18);
    }

    &-desc {
      line-height: calc(26 / 16);
    }
  }

  @media screen and (max-width: 767.98px) {
    padding: 80px 0;

    &-caption {
      margin-bottom: 80px;
    }

    &-sections {
      gap: 40px;
    }

    &-section {
      flex-direction: column;
      gap: 20px;

      &:nth-child(even) {
        flex-direction: column;
      }
    }
  }
}