.testimonial {
  padding: 140px 0 52px;

  &-heading {
    text-align: center;
    margin: 0 auto 76px;
    max-width: 596px;
  }

  &-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0 77px;
  }

  &-image {
    height: 470px;
    width: 100%;
    max-width: 502px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border: 3px solid #b0b0b0;
      border-radius: 42px;
    }
  }

  &-content {
    flex: 1;
  }

  &-text {
    font-size: 23px;
    line-height: calc(40 / 23);
    margin-bottom: 27px;
  }

  &-author {
    color: var(--gray7d);
    font-size: 18px;
    margin-bottom: 14px;
    font-weight: 300;
  }

  &-rating {
    font-size: 18px;
    margin-top: 10px;
  }

  @media screen and (max-width: 1023.98px) {
    padding: 60px 0;

    &-heading {
      margin-bottom: 40px;
    }

    &-main {
      flex-direction: column;
      gap: 40px 0;
    }

    &-image {
      height: auto;
    }

    &-text {
      font-size: 16px;
    }
  }
}