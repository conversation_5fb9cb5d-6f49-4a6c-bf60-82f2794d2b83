*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 62.5%;
}

html,
body {
  height: 100%;
}

body {
  line-height: 1.5;
  font-size: 1.6rem;
  font-family: "Roboto", sans-serif;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.file {
  margin: 5rem auto;
  width: 10rem;
  height: 10rem;
  border-radius: 1rem;
  border: solid 1px #fc566f;
  background-color: rgba(253, 156, 132, 0.5);
  position: relative;
  color: white;
}
.file__input {
  display: none;
}
.file__label {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  flex-wrap: nowrap;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
  z-index: 2;
}

/*# sourceMappingURL=style.css.map */
