*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
  font-family: "Roboto", sans-serif;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.grid-layout div {
  padding: 50px;
  color: white;
  text-transform: uppercase;
  font-size: 20px;
}

.header {
  background-color: black;
}
.menu {
  background-color: red;
}
.hero {
  background-color: blue;
}
.main {
  background-color: gray;
}
.banner {
  background-color: yellow;
}
.extra {
  background-color: green;
}
.image {
  background-color: cyan;
}

@media screen and (min-width: 768px) {
  .grid-layout {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: 100px 300px 300px 150px 150px;
    /* header header header header
        hero   hero   hero    hero
        menu  main    main   main
        menu banner   banner banner
        menu  extra   extra image
    */
    grid-template-areas:
      "header header header header"
      "hero   hero   hero    hero"
      "menu  main    main   main"
      "menu banner   banner banner"
      "menu  extra   extra image";
  }

  .header {
    grid-area: header;
  }

  .hero {
    grid-area: hero;
  }

  .menu {
    grid-area: menu;
  }

  .banner {
    grid-area: banner;
  }

  .extra {
    grid-area: extra;
  }

  .main {
    grid-area: main;
  }

  .image {
    grid-area: image;
  }
}

@media screen and (min-width: 1280px) {
  .grid-layout {
    grid-template-areas:
      "header menu menu menu"
      "hero   hero hero hero"
      "main  main  .   image"
      "main main  . extra"
      "banner banner banner banner";
    grid-template-rows: 100px 500px 200px 200px 200px;
  }
}
