$blue-color: #42a6fe;

@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

html {
  font-size: 62.5%;
}

body {
  font-size: 1.6rem;
  font-family: "Poppins", sans-serif;
  font-weight: normal;
  background-color: #f3faff;
}

a {
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
}

.dropdown {
  width: 40rem;
  margin: 2rem auto 0;
  position: relative;

  &__select {
    padding: 2rem 2.5rem 2rem 4rem;
    background-color: $blue-color;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  &__text {
    margin-left: 3.5rem;
  }

  &__caret {
    margin-left: auto;
  }

  &__item {
    padding: 2rem 2.5rem 2rem 4rem;
    display: flex;
    align-items: center;
    border-left: solid 3px transparent;
    cursor: pointer;
    transition: all 0.2s linear;

    &:not(:last-child) {
      border-bottom: solid 1px #eee;
    }

    &:hover {
      color: #74809d;
      border-left-color: $blue-color;
    }
  }

  &__list {
    position: absolute;
    left: 0;
    top: 100%;
    width: 100%;
    margin-top: 3rem;
    box-shadow: 0 0 10px 0 rgba(black, 0.1);
    transition: all 0.25s linear;
    opacity: 0;
    visibility: hidden;
    background-color: white;

    &::before {
      content: "";
      position: absolute;
      @include size(2rem);
      top: 0;
      right: 2rem;
      border-radius: 4px;
      background-color: #fff;
      transform: translateY(-50%) rotate(45deg);
    }

    &::after {
      content: "";
      position: absolute;
      @include size(100%, 3rem);
      top: 0;
      left: 0;
      background-color: #f3faff;
      transform: translateY(-100%);
      z-index: -1;
    }
  }

  &:hover {
    .dropdown__list {
      opacity: 1;
      visibility: visible;
    }
  }
}