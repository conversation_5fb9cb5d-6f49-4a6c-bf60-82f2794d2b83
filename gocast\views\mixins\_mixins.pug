mixin menuItem(text, icon = "", href = "#")
  li.menu-item
    a.menu-link(href= href)
      span.menu-icon 
        if icon
          case icon
            when "icon-home"
              include ../icons/_icon-home
            when "icon-chart"
              include ../icons/_icon-chart
            when "icon-cube"
              include ../icons/_icon-cube
            when "icon-play"
              include ../icons/_icon-play
            when "icon-subscriptions"
              include ../icons/_icon-subscriptions
            when "icon-discovery"
              include ../icons/_icon-discovery
            when "icon-microphone2"
              include ../icons/_icon-microphone2
            when "icon-3user"
              include ../icons/_icon-3user
            when "icon-add-user"
              include ../icons/_icon-add-user
            when "icon-medal-star"
              include ../icons/_icon-medal-star
      span.menu-text= text

mixin toggle()
  label.toggle
    input(type="checkbox", name="feed-toggle", id="feed-toggle")
    .toggle-main
      .toggle-spinner

mixin pageHeader(title = "Trending", description = "")
  .page-header
    h2.page-header-heading= title
    p.page-header-desc= description

mixin button(icon = false, title = "Play Episode")
  button.button.button--primary 
    if icon
      include ../icons/_icon-trending-play
    span= title

mixin feedItem(author_avatar = "./images/img-host-user-6.png", author_name = "James Killened", author_action = "rating", feed_image = "./images/img-feed-1.png", feed_perspective = "Perspective Podcast (Ep. 1)", feed_title = "Philosophy vs Religion vs Science: Yahia Amin and Sadman Sadik", feed_date = "11 dayes ago", feed_time = "11 dayes ago", feed_duration = "45mins 10 Sec", feed_desc = "Play Philosophy vs Religion vs Science: Perspective Podcast (Ep. 1) | Yahia Amin and Sadman Sadik by PERSPECTIVE with Yahia Amin on desktop and mobile. Play over 265 million tracks for..")
  .feed-item
    .feed-item-header
      .feed-author 
        .feed-author-avatar
          img(src=author_avatar, alt="author avatar")
          span.feed-author-verify
            include ../icons/_icon-verify
        .feed-author-content 
          .feed-author-header
            h4.feed-author-name= author_name
            if author_action === "rating"
              .feed-author-rating
                span rated an episude
                img(srcSet="./images/img-rating.png 2x", alt="rating")
            if author_action === "release"
              .feed-author-rating
                span released an episode
          .feed-date= feed_date
      span
        include ../icons/_icon-more
    .feed-item-main
      .feed-image
        img(src=feed_image, alt="feed image")
      .feed-item-content
        p.feed-item-podcast 
          span= feed_perspective
          include ../icons/_icon-share
        h3.feed-item-heading= feed_title
        .feed-item-time
          .feed-item-time-item
            include ../icons/_icon-time
            span= feed_time
          .feed-item-time-item
            include ../icons/_icon-timer
            span= feed_duration
        p.feed-item-desc= feed_desc
    .feed-item-footer
      .feed-actions
        button
          include ../icons/_icon-heart
        button
          include ../icons/_icon-magic-star
        button
          include ../icons/_icon-music-playlist
      +button(true)

mixin widgetHeader(heading = "", url = "#")
  .widget-header
    h3.widget-heading= heading
    a.widget-view-all(href= url) View All

mixin followItem(avatar = "", name = "", username = "")
  .follow
    .follow-user
      .follow-user-avatar
        img(src=avatar, alt="follow user avatar")
      .follow-user-info
        h4.follow-user-name= name
        p.follow-user-username= username
    +button(false, "Follow")

mixin listenItem(image = "", perspective = "", title = "", play = "", heart = "")
  .listen
    .listen-image 
      img(src=image, alt="listen image")
    .listen-info 
      span.listen-perspective= perspective
      h4.listen-title= title
      .listen-meta
        .listen-meta-item
          include ../icons/_icon-play2
          span= play
        .listen-meta-item
          include ../icons/_icon-heart
          span= heart

mixin topicItem(image = "", title = "", podcast = "")
  .topic
    img.topic-image(src=image, alt="topic image")
    .topic-content
      h4.topic-title= title
      .topic-podcast= podcast

mixin playlistItem(image = "./images/playlist-1.png", title = "UIHUT Podcsat", date = "Created on 1 day ago", episodes = 41, likes = 1.5, desc = "UIHUT is a design and coding resources platform for designers, developers, and entrepreneurs.")
  .playlist-item
    .playlist-image
      img(src=image, alt="playlist image")
    .playlist-content
      .playlist-top
        h3.playlist-title= title
        span.playlist-date= date
      .playlist-desc= desc
      .playlist-bottom
        .playlist-episodes
          each val in ["./images/playlist-episode-1.png", "./images/playlist-episode-2.png", "./images/playlist-episode-3.png", "./images/playlist-episode-4.png"]
            img.playlist-episode-image.playlist-episode-item(src=val, alt="playlist episode image")
          .playlist-episode-number.playlist-episode-item= episodes + "+"
        .playlist-likes
          include ../icons/_icon-heart-fill
          span.playlist-likes-count= likes + "k"