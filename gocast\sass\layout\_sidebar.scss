@use "../abstracts" as abst;

.sidebar {
  padding: 48px 30px;

  @include abst.screenMaxWidth(1279.98px) {
    position: fixed;
    top: 0;
    left: 0;
    width: 300px;
    bottom: 0;
    z-index: 9;
    transition: transform 0.25s linear;
    transform: translateX(-100%);
  }
}

.logo {
  display: block;
  margin-bottom: 44px;
  color: var(--logo-color, #11142d);
}

.menu {
  display: flex;
  flex-direction: column;
  gap: 6px;
  --gap: 26px;

  &:not(:last-child)::after {
    content: "";
    display: block;
    height: 1px;
    background-color: abst.$gray-e9;
    margin-block: var(--gap);
  }

  &-heading {
    font-size: 13px;
    margin-bottom: 10px;
    padding-left: 20px;
  }

  &-icon {
    flex: 0 0 24px;
  }

  &-link {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    line-height: 143%;
    font-weight: 500;
    padding: 8px 30px 8px 20px;
    border-radius: 12px;

    [fill^="#E5"] {
      fill: var(--icon-path, #e5eaf1);
    }

    &:hover {
      background: linear-gradient(92deg, #415EF4 0.33%, #7141EF 28.14%);
      color: #fff;

      [fill^="#E5"] {
        fill: #fff;
        opacity: 1;
      }

      [fill^="#A5"] {
        mix-blend-mode: normal;
        fill: hsl(var(--primary-h), calc(var(--primary-s) + 1%), calc(var(--primary-l) + 16%));
      }
    }
  }
}