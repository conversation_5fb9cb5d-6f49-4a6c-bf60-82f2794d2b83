*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
  height: 100vh;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(black, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;

  // Create a blurred background
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  // Create a bright background
  backdrop-filter: brightness(3);
  -webkit-backdrop-filter: brightness(3);

  // Create a gray background
  backdrop-filter: grayscale(10);
  -webkit-backdrop-filter: grayscale(10);

  backdrop-filter: opacity(0.5);

  backdrop-filter: blur(2px);
}

.content {
  width: 70vh;
  height: 70vh;
  padding: 50px;
  background-color: #fff;
}

:root {
  --ratio: 16/9;
  // 16 / 9 = 1.77778
}

.media {
  height: 0;
  position: relative;
  // 100% / 1.77778 = 56.25%
  padding-bottom: 56.25%;
  padding-bottom: calc(100% / (var(--ratio)));

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}