.customer {
  .boxed {
    margin-bottom: 60px;
  }

  .heading {
    max-width: 447px;
    margin-left: auto;
    margin-right: auto;
  }

  &-item {
    border: solid 2px $gray-lighter-color;
    border-radius: 10px;
    padding: 30px;
    margin: 0 25px;

    &.slick-current.slick-active {
      border-color: $primary-color;
    }
  }

  &-top {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-info {
    display: flex;
    align-items: center;
    gap: 0 20px;
  }

  &-avatar {
    width: 50px;
    height: 50px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 100rem;
    }
  }

  &-name,
  &-address {
    line-height: 1;
  }

  &-rating {
    display: flex;
    align-items: center;
    gap: 0 10px;
  }

  // Slick slider
  .slick-track {
    display: flex;
    padding-bottom: 60px;
  }

  .slick-dots {
    list-style-type: none;
    display: flex;
    align-items: center;
    gap: 0 15px;
    transform: translateY(50%);

    button {
      font-size: 0;
      background-color: $gray-color;
      width: 15px;
      height: 15px;
      border-radius: 100rem;
      transition: all 0.2s linear;
    }

    .slick-active button {
      background-color: $primary-color;
      width: 45px;
    }
  }

  .slick-arrow {
    width: 60px;
    height: 60px;
    border-radius: 100rem;
    display: flex !important;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 0;
    transform: translateY(50%);
    z-index: 10;
  }

  .slick-prev {
    border: solid 1px $primary-color;
    background-color: transparent;
    right: 80px;
  }

  .slick-next {
    background-color: $primary-color;
    right: 0;
  }
}

@media screen and (max-width: 1023px) {
  .customer {
    .slick-track {
      padding-bottom: 20px;
    }

    .slick-dots {
      justify-content: center;
    }
  }

  .customer-list {
    padding-bottom: 50px;
  }
}