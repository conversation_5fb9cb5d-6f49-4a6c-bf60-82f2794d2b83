@use "../abstracts" as abst;

.topbar {
  padding: 24px 56px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &-logo {
    flex-shrink: 0;
    color: #fff;
  }

  &-right {
    display: flex;
    align-items: center;
    gap: 24px;

    &>* {
      flex-shrink: 0;
    }
  }
}

.profile {
  display: block;
  @include abst.size(48px);

  img {
    @include abst.rounded("rounded-full");
    @include abst.cover;
  }
}

.button-upload {
  --gap: 6px;
  --p: 0 16px;
  height: 48px;
}

.search {
  padding: 12px 20px;
  @include abst.rounded("rounded-lg");
  @include abst.flexBox(center, null, 12px);
  flex: 0 1 360px;

  &-input {
    @include abst.bg(transparent);
    flex: 1;
    border: none;
    font-weight: 500;
    font-size: 15px;
    color: abst.$primary-color;
    caret-color: abst.$primary-color;
    @include abst.placeholder(abst.$gray2);
  }
}

@media screen and (min-width: 1280px) {
  .topbar-logo {
    display: none;
  }
}


@media screen and (max-width: 1279.98px) {
  .topbar {
    padding: 20px 24px;
  }

  .topbar-action,
  .search,
  .button-upload {
    display: none;
  }
}