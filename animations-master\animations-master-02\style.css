*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
}

body {
  line-height: 1.5;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.circles {
  text-align: center;
  margin: 5rem;
  animation: loading 3s infinite;
}

@keyframes loading {
  50% {
    transform: rotate(200deg);
  }

  75% {
    transform: rotate(160deg);
  }

  100% {
    transform: rotate(180deg);
  }
}

.circle-item {
  width: 3rem;
  height: 3rem;
  border: 3px solid violet;
  border-radius: 3rem;
  display: inline-block;
  margin: 0 0.5rem;
}

.circle-item:first-child {
  border-color: cyan;
}
