*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
}

body {
  line-height: 1.5;
  font-family: "Roboto", sans-serif;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

body {
  background-color: #131933;
}

.boxed {
  width: 10rem;
  height: 4rem;
  margin: 5rem;
  background-image: linear-gradient(to right, #fa4c53, #fd9c84);
  border-radius: 10rem;
  padding: 10px;
}
.boxed-child {
  width: 100%;
  height: 100%;
  background-color: #131933;
  border-radius: inherit;
}

/*# sourceMappingURL=style.css.map */
