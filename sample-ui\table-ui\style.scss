*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 62.5%;
}

html,
body {
  height: 100%;
}

body {
  font-size: 1.6rem;
  line-height: 1.5;
  font-family: "Poppins", sans-serif;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
  outline: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

table {
  width: 100%;

  th {
    text-transform: uppercase;
    font-weight: 800;
  }

  td,
  th {
    text-align: center;
    padding: 2rem;
    color: #9c84a9;
    font-size: 1.5rem;
  }

  thead tr {
    border-bottom: solid 1px #ccc;
  }

  .checkbox {
    &__input {
      display: none;

      &:checked+.checkbox__label::after {
        opacity: 1;
        visibility: visible;
      }
    }

    &__label {
      cursor: pointer;
      position: relative;

      &::before {
        content: "";
        @include size(2rem);
        border-radius: 5px;
        background-color: #ccc;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
      }

      &::after {
        content: "";
        @include size(1rem, 0.5rem);
        position: absolute;
        left: 5px;
        top: 50%;
        transform: translateY(-50%) rotate(-45deg);
        border-bottom: solid 2.5px #fff;
        border-left: solid 2.5px #fff;
        transition: 0.25s linear;
        opacity: 0;
        visibility: hidden;
      }
    }
  }

  .desc {
    white-space: nowrap;
    max-width: 15rem;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .toggle {
    &__input {
      display: none;

      &:checked+.toggle__label {
        background-color: #00aefd;

        &::before {
          background-color: #00aefd;
          transform: translate(3rem, -50%);
        }
      }
    }

    &__label {
      width: 5rem;
      height: 5px;
      border-radius: 1rem;
      background-color: #ccc;
      display: inline-block;
      position: relative;
      cursor: pointer;
      transition: all 0.25s linear;

      &::before {
        content: "";
        @include size(2rem);
        border-radius: 1rem;
        background-color: #ccc;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        transition: all 0.25s linear;
      }
    }
  }

  .status {
    padding: 1rem 3rem;
    display: inline-block;
    border-radius: 2rem;
    font-size: 1.4rem;
    font-weight: bold;
    cursor: pointer;
    text-align: center;

    &--active {
      color: green;
      background-color: rgba(green, 0.2);
    }
  }

  .action {
    display: flex;

    &__icon {
      cursor: pointer;
      margin: 0 5px;
    }
  }
}

@media screen and (max-width: 1279px) {
  .table {
    width: 100%;
    overflow-x: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}